<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class LineMessagingService
{
    /**
     * LINE Messaging APIのエンドポイント
     */
    protected $apiUrl = 'https://api.line.me/v2/bot/message/push';

    /**
     * チャネルアクセストークン
     */
    protected $channelToken;

    /**
     * ユーザーID
     */
    protected $userId;

    /**
     * コンストラクタ
     */
    public function __construct()
    {
        $this->channelToken = env('LINE_CHANNEL_TOKEN');
        $this->userId = env('LINE_USER_ID');
    }

    /**
     * LINEにメッセージを送信
     *
     * @param string $message 送信するメッセージ
     * @return bool 送信成功したかどうか
     */
    public function sendMessage(string $message): bool
    {
        if (empty($this->channelToken) || empty($this->userId)) {
            Log::error('LINE Messaging API設定が不足しています。', [
                'channel_token_exists' => !empty($this->channelToken),
                'user_id_exists' => !empty($this->userId)
            ]);
            return false;
        }

        try {
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $this->channelToken
            ])->post($this->apiUrl, [
                'to' => $this->userId,
                'messages' => [
                    [
                        'type' => 'text',
                        'text' => $message
                    ]
                ]
            ]);

            if ($response->successful()) {
                Log::info('LINE通知を送信しました。', [
                    'message' => $message,
                    'response' => $response->json()
                ]);
                return true;
            } else {
                Log::error('LINE通知の送信に失敗しました。', [
                    'message' => $message,
                    'status' => $response->status(),
                    'response' => $response->json()
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('LINE通知の送信中にエラーが発生しました。', [
                'message' => $message,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
