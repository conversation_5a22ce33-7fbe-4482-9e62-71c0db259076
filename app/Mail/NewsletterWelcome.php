<?php

namespace App\Mail;

use App\Models\Newsletter;
use App\Models\NewsletterSubscriber;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\URL;

class NewsletterWelcome extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(
        public NewsletterSubscriber $subscriber,
        public Newsletter $newsletter,
        public ?string $giftUrl = null
    ) {}

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: "【プレゼント】{$this->newsletter->title} - 登録ありがとうございます タロティーク",
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        // 購読解除用のトークンを生成（ここでは簡易的に購読者IDを使用）
        $token = base64_encode($this->subscriber->id . '-' . $this->subscriber->email);
        $unsubscribeUrl = URL::route('newsletter.unsubscribe', [
            'token' => $token,
            'email' => $this->subscriber->email
        ]);

        return new Content(
            view: 'emails.newsletter-welcome',
            with: [
                'subscriber' => $this->subscriber,
                'newsletter' => $this->newsletter,
                'giftUrl' => $this->giftUrl,
                'unsubscribeUrl' => $unsubscribeUrl,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
