<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NewsletterSubscriber extends Model
{
    protected $fillable = [
        'email',
        'name',
        'status', // active, unsubscribed
        'unsubscribed_at',
        'newsletter_id',
    ];

    protected $casts = [
        'unsubscribed_at' => 'datetime',
    ];

    /**
     * アクティブな購読者のみを取得するスコープ
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * この購読者が登録したニュースレターを取得
     */
    public function newsletter(): BelongsTo
    {
        return $this->belongsTo(Newsletter::class);
    }
}
