<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Course extends Model
{
    protected $fillable = [
        'name',
        'description',
        'price',
        'level',
    ];

    /**
     * このコースを購入したユーザーを取得
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'course_users')
            ->withTimestamps();
    }

    /**
     * このコースの受講進捗を取得
     */
    public function progress()
    {
        return $this->hasMany(CourseProgress::class);
    }

    /**
     * このコースのコンテンツを取得
     */
    public function contents()
    {
        return $this->hasMany(CourseContent::class);
    }

    /**
     * このコースの修了証を取得
     */
    public function certificates()
    {
        return $this->hasMany(Certificate::class);
    }

    /**
     * このコースの卒業生（修了証を持つユーザー）を取得
     */
    public function graduates()
    {
        return User::whereHas('certificates', function ($query) {
            $query->where('course_id', $this->id)
                  ->where('is_public', true);
        })->get();
    }

    /**
     * コースIDに基づいてコース名を取得する静的メソッド
     */
    public static function getNameById(int $id): string
    {
        $course = self::find($id);
        return $course ? $course->name : '不明なコース';
    }

    /**
     * コースIDに基づいて価格を取得する静的メソッド
     */
    public static function getPriceById(int $id): int
    {
        $course = self::find($id);
        return $course ? $course->price : 0;
    }

    /**
     * コースIDに基づいてレベルを取得する静的メソッド
     */
    public static function getLevelById(int $id): string
    {
        $course = self::find($id);
        return $course ? $course->level : '不明';
    }
}
