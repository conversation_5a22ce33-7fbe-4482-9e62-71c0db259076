<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NewsletterLog extends Model
{
    protected $fillable = [
        'newsletter_id',
        'recipient_email',
        'status', // sent, failed, opened
        'error_message',
    ];

    /**
     * このログが属するニュースレターを取得
     */
    public function newsletter(): BelongsTo
    {
        return $this->belongsTo(Newsletter::class);
    }
}
