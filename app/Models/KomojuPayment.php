<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class KomojuPayment extends Model
{
    protected $fillable = [
        'session_id',
        'payment_id',
        'order_id',
        'status',
        'amount',
        'currency',
        'payment_method',
        'customer_name',
        'customer_email',
        'student_id',
        'payment_data'
    ];

    protected $casts = [
        'payment_data' => 'array',
        'amount' => 'integer'
    ];

    /**
     * 関連する学生を取得
     */
    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * KOMOJUのステータスをアプリケーションのステータスに変換
     *
     * @param string $komojuStatus KOMOJUのステータス
     * @return string アプリケーションのステータス
     */
    public static function mapKomojuStatus(string $komojuStatus): string
    {
        switch ($komojuStatus) {
            case 'completed':
                return 'completed';
            case 'pending':
                return 'pending';
            case 'failed':
                return 'failed';
            case 'cancelled':
            case 'canceled':
                return 'cancelled';
            case 'refunded':
                return 'refunded';
            default:
                return 'pending';
        }
    }

    /**
     * KOMOJUの決済情報をデータベースに保存または更新
     *
     * @param array $paymentData 決済データ
     * @return KomojuPayment 保存された決済情報
     */
    public static function storePaymentData(array $paymentData): KomojuPayment
    {
        // セッションIDまたは決済IDで既存のレコードを検索
        $payment = null;
        if (!empty($paymentData['session_id'])) {
            $payment = self::where('session_id', $paymentData['session_id'])->first();
        } elseif (!empty($paymentData['payment_id'])) {
            $payment = self::where('payment_id', $paymentData['payment_id'])->first();
        }

        // レコードが存在しない場合は新規作成
        if (!$payment) {
            $payment = new self();
        }

        // 決済情報を設定
        $payment->session_id = $paymentData['id'] ?? $paymentData['session_id'] ?? null;

        // payment_idはpaymentオブジェクト内にある場合がある
        if (!empty($paymentData['payment']) && !empty($paymentData['payment']['id'])) {
            $payment->payment_id = $paymentData['payment']['id'];
        } else {
            $payment->payment_id = $paymentData['payment_id'] ?? null;
        }

        $payment->order_id = $paymentData['external_order_num'] ?? null;
        $payment->status = $paymentData['status'] ?? 'pending';
        $payment->amount = $paymentData['amount'] ?? null;
        $payment->currency = $paymentData['currency'] ?? 'JPY';

        // payment_methodはpayment.payment_details.typeに格納されている場合がある
        if (!empty($paymentData['payment']) && !empty($paymentData['payment']['payment_details']) && !empty($paymentData['payment']['payment_details']['type'])) {
            $payment->payment_method = $paymentData['payment']['payment_details']['type'];
        } else {
            $payment->payment_method = $paymentData['payment_method'] ?? null;
        }

        // メタデータから顧客情報を取得
        if (!empty($paymentData['metadata'])) {
            $payment->customer_name = $paymentData['metadata']['name'] ?? null;
            $payment->customer_email = $paymentData['metadata']['email'] ?? null;

            // 学生IDがメタデータに含まれている場合は設定
            if (!empty($paymentData['metadata']['student_id'])) {
                $payment->student_id = $paymentData['metadata']['student_id'];
            }
        }

        // 決済データをJSON形式で保存
        $payment->payment_data = $paymentData;

        // 保存
        $payment->save();

        // 関連する学生レコードを更新
        self::updateRelatedStudent($payment);

        return $payment;
    }

    /**
     * 関連する学生レコードを更新
     *
     * @param KomojuPayment $payment 決済レコード
     * @return void
     */
    private static function updateRelatedStudent(KomojuPayment $payment): void
    {
        // order_idから学生を検索
        if ($payment->order_id) {
            $student = Student::where('order_id', $payment->order_id)->first();

            if ($student) {
                // 学生IDを設定
                $payment->student_id = $student->id;
                $payment->save();

                // 学生の決済情報を更新
                $student->payment_id = $payment->payment_id;
                $student->payment_status = self::mapKomojuStatus($payment->status);
                $student->payment_type = $payment->payment_method ?? 'Card';
                $student->amount = $payment->amount;

                // payment_responseフィールドに決済情報をJSON形式で保存
                $responseInfo = [
                    'payment_info' => $payment->payment_data,
                    'timestamp' => now()->toDateTimeString(),
                ];
                $student->payment_response = json_encode($responseInfo);

                $student->save();
            }
        }

        // student_idが直接設定されている場合
        if ($payment->student_id) {
            $student = Student::find($payment->student_id);

            if ($student) {
                // 学生の決済情報を更新（order_idが一致しない場合のみ）
                if (!$student->payment_id || $student->payment_id !== $payment->payment_id) {
                    $student->payment_id = $payment->payment_id;
                    $student->payment_status = self::mapKomojuStatus($payment->status);
                    $student->payment_type = $payment->payment_method ?? 'Card';
                    $student->amount = $payment->amount;

                    // payment_responseフィールドに決済情報をJSON形式で保存
                    $responseInfo = [
                        'payment_info' => $payment->payment_data,
                        'timestamp' => now()->toDateTimeString(),
                    ];
                    $student->payment_response = json_encode($responseInfo);

                    $student->save();
                }
            }
        }
    }
}
