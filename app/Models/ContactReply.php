<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ContactReply extends Model
{
    protected $fillable = [
        'contact_id',
        'message',
        'sent_at',
        'status', // sent, failed
        'error_message',
    ];

    protected $casts = [
        'sent_at' => 'datetime',
    ];

    /**
     * この返信が属するお問い合わせを取得
     */
    public function contact(): BelongsTo
    {
        return $this->belongsTo(Contact::class);
    }
}
