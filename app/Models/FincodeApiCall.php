<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class FincodeApiCall extends Model
{
    protected $fillable = [
        'type',
        'last_called_at',
        'records_fetched',
        'response_data',
        'status'
    ];

    protected $casts = [
        'last_called_at' => 'datetime',
        'response_data' => 'array',
    ];

    /**
     * 指定されたタイプのAPIコールが必要かどうかを判断
     * 1時間以上前の場合はtrueを返す
     *
     * @param string $type APIコールのタイプ
     * @return bool APIコールが必要かどうか
     */
    public static function isCallNeeded(string $type = 'sales'): bool
    {
        $lastCall = self::where('type', $type)
            ->orderBy('last_called_at', 'desc')
            ->first();

        // APIコールの記録がない場合は必要
        if (!$lastCall) {
            return true;
        }

        // 最後のAPIコールから1時間以上経過している場合は必要
        return $lastCall->last_called_at->diffInMinutes(now()) >= 60;
    }

    /**
     * APIコールの記録を更新または作成
     *
     * @param string $type APIコールのタイプ
     * @param int $recordsFetched 取得したレコード数
     * @param array|null $responseData レスポンスデータ
     * @param string $status ステータス
     * @return self
     */
    public static function recordCall(string $type = 'sales', int $recordsFetched = 0, ?array $responseData = null, string $status = 'success'): self
    {
        return self::create([
            'type' => $type,
            'last_called_at' => now(),
            'records_fetched' => $recordsFetched,
            'response_data' => $responseData,
            'status' => $status
        ]);
    }

    /**
     * 最後のAPIコール情報を取得
     *
     * @param string $type APIコールのタイプ
     * @return self|null
     */
    public static function getLastCall(string $type = 'sales'): ?self
    {
        return self::where('type', $type)
            ->orderBy('last_called_at', 'desc')
            ->first();
    }
}
