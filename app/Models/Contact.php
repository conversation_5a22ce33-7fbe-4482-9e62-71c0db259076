<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Contact extends Model
{
    protected $fillable = [
        'name',
        'email',
        'subject',
        'message',
        'source', // form, imap
        'status', // new, replied, closed
        'message_id', // IMAPメッセージID（IMAPの場合）
    ];

    /**
     * このお問い合わせの返信を取得
     */
    public function replies(): HasMany
    {
        return $this->hasMany(ContactReply::class);
    }
}
