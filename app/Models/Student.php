<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Student extends Model
{
    protected $fillable = [
        'name',
        'email',
        'phone',
        'address',
        'order_id',
        'course_id',
        'payment_status',
        'payment_type',
        'payment_id',
        'amount',
        'payment_response'
    ];

    protected $casts = [
        'payment_response' => 'array',
    ];

    /**
     * この受講者が購入したコースを取得
     */
    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * 支払いステータスが完了しているか確認
     */
    public function isPaymentCompleted()
    {
        return $this->payment_status === 'completed';
    }

    /**
     * 返金処理を行う
     */
    public function refund()
    {
        // 実装予定
        $this->payment_status = 'refunded';
        $this->save();
        return true;
    }
}
