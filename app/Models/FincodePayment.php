<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class FincodePayment extends Model
{
    protected $fillable = [
        'payment_id',
        'order_id',
        'shop_id',
        'access_id',
        'transaction_id',
        'status',
        'pay_type',
        'amount',
        'card_no',
        'brand',
        'holder_name',
        'expire',
        'method',
        'pay_times',
        'job_code',
        'client_field_1',
        'client_field_2',
        'client_field_3',
        'process_date',
        'created',
        'updated',
        'payment_data',
        'student_id'
    ];

    protected $casts = [
        'process_date' => 'datetime',
        'created' => 'datetime',
        'updated' => 'datetime',
        'payment_data' => 'array',
        'amount' => 'integer'
    ];

    /**
     * 関連する学生を取得
     */
    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * ステータスの日本語表示を取得
     */
    public function getStatusTextAttribute()
    {
        switch($this->status) {
            case 'UNPROCESSED': return '未処理';
            case 'CHECKED': return '有効性チェック済み';
            case 'AUTHORIZED': return '仮売上';
            case 'AWAITING_CUSTOMER_PAYMENT': return '顧客支払い待ち';
            case 'AWAITING_PAYMENT_APPROVAL': return '支払い承認待ち';
            case 'CAPTURED': return '売上確定';
            case 'CANCELED': return 'キャンセル';
            case 'EXPIRED': return '期限切れ';
            case 'AUTHENTICATED': return '未処理（3Dセキュア認証待ち）';
            case 'FAILED': return '失敗';
            default: return $this->status ?? '未設定';
        }
    }

    /**
     * 請求タイプの日本語表示を取得
     */
    public function getJobCodeTextAttribute()
    {
        switch($this->job_code) {
            case 'AUTH': return '仮売上';
            case 'CAPTURE': return '売上確定';
            case 'SALES': return '売上';
            default: return $this->job_code ?? '未設定';
        }
    }

    /**
     * 支払い方法の日本語表示を取得
     */
    public function getMethodTextAttribute()
    {
        switch($this->method) {
            case '1': return '一括払い';
            case '2': return '分割払い';
            default: return $this->method ?? '未設定';
        }
    }

    /**
     * 決済データをFincodeから取得して保存
     *
     * @param array $params APIパラメータ
     * @param bool $forceUpdate 強制的に更新するかどうか
     * @return array 処理結果
     */
    public static function fetchAndStoreFromFincode(array $params = [], bool $forceUpdate = false): array
    {
        // APIコール記録を確認
        if (!$forceUpdate && !FincodeApiCall::isCallNeeded('sales')) {
            return [
                'success' => true,
                'message' => '前回の取得から1時間経過していないため、更新をスキップしました。',
                'updated' => false,
                'new_records' => 0
            ];
        }

        // デフォルトパラメータ
        $defaultParams = [
            'pay_type' => 'Card', // 必須パラメータ: カード決済を指定
            'limit' => 50,
            'sort' => 'process_date' // 処理日時の昇順
        ];

        // パラメータをマージ
        $params = array_merge($defaultParams, $params);

        try {
            // 環境に応じてAPIのURLを設定
            $environment = env('FINCODE_ENVIRONMENT', 'test');
            $apiBaseUrl = $environment === 'production'
                ? 'https://api.fincode.jp'
                : 'https://api.test.fincode.jp';

            // APIヘッダーを設定
            $apiHeaders = [
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . env('FINCODE_API_SECRET'),
            ];

            // クエリパラメータの構築
            $queryString = http_build_query($params);

            // Fincode APIを呼び出して決済情報を検索
            $endpoint = '/v1/payments?' . $queryString;
            $apiUrl = $apiBaseUrl . $endpoint;

            // cURLを使用してAPIリクエストを実行
            $ch = curl_init($apiUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . env('FINCODE_API_SECRET')
            ]);

            $responseBody = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            // HTTPステータスコードが200以外の場合はエラー
            if ($httpCode !== 200) {
                // APIコール記録を保存
                FincodeApiCall::recordCall('sales', 0, null, 'error');

                return [
                    'success' => false,
                    'message' => 'Fincode APIエラー: ' . $responseBody,
                    'updated' => false,
                    'new_records' => 0
                ];
            }

            // レスポンスをJSON形式に変換
            $paymentData = json_decode($responseBody, true);

            // レスポンスが正しくJSONに変換できなかった場合はエラー
            if (json_last_error() !== JSON_ERROR_NONE) {
                // APIコール記録を保存
                FincodeApiCall::recordCall('sales', 0, null, 'error');

                return [
                    'success' => false,
                    'message' => '無効なJSONレスポンス: ' . json_last_error_msg(),
                    'updated' => false,
                    'new_records' => 0
                ];
            }

            // 取得した決済データを処理
            $newRecords = self::processPaymentData($paymentData);

            // APIコール記録を保存
            FincodeApiCall::recordCall(
                'sales',
                count($paymentData['list'] ?? []),
                [
                    'total_count' => $paymentData['total_count'] ?? 0,
                    'count' => count($paymentData['list'] ?? []),
                    'timestamp' => now()->toDateTimeString()
                ],
                'success'
            );

            return [
                'success' => true,
                'message' => '決済データを更新しました。新規レコード: ' . $newRecords,
                'updated' => true,
                'new_records' => $newRecords,
                'total_count' => $paymentData['total_count'] ?? 0
            ];

        } catch (\Exception $e) {
            // APIコール記録を保存
            FincodeApiCall::recordCall('sales', 0, null, 'error');

            return [
                'success' => false,
                'message' => 'Fincode APIエラー: ' . $e->getMessage(),
                'updated' => false,
                'new_records' => 0
            ];
        }
    }

    /**
     * 取得した決済データを処理して保存
     *
     * @param array $paymentData Fincodeから取得した決済データ
     * @return int 新規に追加されたレコード数
     */
    private static function processPaymentData(array $paymentData): int
    {
        $newRecords = 0;
        $paymentList = $paymentData['list'] ?? [];

        foreach ($paymentList as $payment) {
            // 必要なデータが存在するか確認
            if (empty($payment['id'])) {
                continue;
            }

            // 既存のレコードを検索
            $record = self::where('payment_id', $payment['id'])->first();

            if ($record) {
                // 既存レコードの更新
                $record->update([
                    'order_id' => $payment['order_id'] ?? null,
                    'shop_id' => $payment['shop_id'] ?? null,
                    'access_id' => $payment['access_id'] ?? null,
                    'transaction_id' => $payment['transaction_id'] ?? null,
                    'status' => $payment['status'] ?? '',
                    'pay_type' => $payment['pay_type'] ?? '',
                    'amount' => $payment['amount'] ?? 0,
                    'card_no' => $payment['card_no'] ?? null,
                    'brand' => $payment['brand'] ?? null,
                    'holder_name' => $payment['holder_name'] ?? null,
                    'expire' => $payment['expire'] ?? null,
                    'method' => $payment['method'] ?? null,
                    'pay_times' => $payment['pay_times'] ?? null,
                    'job_code' => $payment['job_code'] ?? null,
                    'client_field_1' => $payment['client_field_1'] ?? null,
                    'client_field_2' => $payment['client_field_2'] ?? null,
                    'client_field_3' => $payment['client_field_3'] ?? null,
                    'process_date' => $payment['process_date'] ?? null,
                    'created' => $payment['created'] ?? null,
                    'updated' => $payment['updated'] ?? null,
                    'payment_data' => $payment
                ]);

                // 関連する学生レコードを更新
                self::updateRelatedStudent($record);
            } else {
                // 新規レコードの作成
                $record = self::create([
                    'payment_id' => $payment['id'],
                    'order_id' => $payment['order_id'] ?? null,
                    'shop_id' => $payment['shop_id'] ?? null,
                    'access_id' => $payment['access_id'] ?? null,
                    'transaction_id' => $payment['transaction_id'] ?? null,
                    'status' => $payment['status'] ?? '',
                    'pay_type' => $payment['pay_type'] ?? '',
                    'amount' => $payment['amount'] ?? 0,
                    'card_no' => $payment['card_no'] ?? null,
                    'brand' => $payment['brand'] ?? null,
                    'holder_name' => $payment['holder_name'] ?? null,
                    'expire' => $payment['expire'] ?? null,
                    'method' => $payment['method'] ?? null,
                    'pay_times' => $payment['pay_times'] ?? null,
                    'job_code' => $payment['job_code'] ?? null,
                    'client_field_1' => $payment['client_field_1'] ?? null,
                    'client_field_2' => $payment['client_field_2'] ?? null,
                    'client_field_3' => $payment['client_field_3'] ?? null,
                    'process_date' => $payment['process_date'] ?? null,
                    'created' => $payment['created'] ?? null,
                    'updated' => $payment['updated'] ?? null,
                    'payment_data' => $payment
                ]);

                // 関連する学生レコードを更新
                self::updateRelatedStudent($record);

                $newRecords++;
            }
        }

        return $newRecords;
    }

    /**
     * 関連する学生レコードを更新
     *
     * @param FincodePayment $payment 決済レコード
     * @return void
     */
    private static function updateRelatedStudent(FincodePayment $payment): void
    {
        // order_idから学生を検索
        if ($payment->order_id) {
            $student = Student::where('order_id', $payment->order_id)->first();

            if ($student) {
                // 学生IDを設定
                $payment->student_id = $student->id;
                $payment->save();

                // 学生の決済情報を更新
                $student->payment_id = $payment->payment_id;
                $student->payment_status = self::mapFincodeStatus($payment->status);
                $student->payment_type = $payment->pay_type;
                $student->amount = $payment->amount;

                // payment_responseフィールドに決済情報をJSON形式で保存
                $responseInfo = [
                    'payment_info' => $payment->payment_data,
                    'timestamp' => now()->toDateTimeString(),
                ];
                $student->payment_response = json_encode($responseInfo);

                $student->save();
            }
        }

        // client_field_3からstudent_idを抽出
        if ($payment->client_field_3) {
            if (preg_match('/student_id:(\d+)/', $payment->client_field_3, $matches)) {
                $studentId = $matches[1];
                $student = Student::find($studentId);

                if ($student) {
                    // 学生IDを設定
                    $payment->student_id = $student->id;
                    $payment->save();

                    // 学生の決済情報を更新（order_idが一致しない場合のみ）
                    if (!$student->payment_id || $student->payment_id !== $payment->payment_id) {
                        $student->payment_id = $payment->payment_id;
                        $student->payment_status = self::mapFincodeStatus($payment->status);
                        $student->payment_type = $payment->pay_type;
                        $student->amount = $payment->amount;

                        // payment_responseフィールドに決済情報をJSON形式で保存
                        $responseInfo = [
                            'payment_info' => $payment->payment_data,
                            'timestamp' => now()->toDateTimeString(),
                        ];
                        $student->payment_response = json_encode($responseInfo);

                        $student->save();
                    }
                }
            }
        }
    }

    /**
     * Fincodeのステータスをアプリケーションのステータスにマッピング
     */
    private static function mapFincodeStatus(string $fincodeStatus): string
    {
        switch ($fincodeStatus) {
            case 'AUTHORIZED':
            case 'CAPTURED':
                return 'completed';
            case 'CANCELED':
                return 'cancelled';
            case 'REFUNDED':
                return 'refunded';
            case 'FAILED':
                return 'failed';
            default:
                return 'pending';
        }
    }
}
