<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Newsletter extends Model
{
    protected $fillable = [
        'title',
        'form_id',
        'image_path',
        'gift_file_path',
        'gift_description',
        'sales_letter',
        'video_url',
        'video_file_path',
        'landing_page_url',
        'content',
        'welcome_message',
        'sent_at',
        'recipients_count',
        'is_active',
    ];

    protected $casts = [
        'sent_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * このニュースレターの送信ログを取得
     */
    public function logs(): HasMany
    {
        return $this->hasMany(NewsletterLog::class);
    }

    /**
     * このニュースレターの購読者を取得
     */
    public function subscribers(): HasMany
    {
        return $this->hasMany(NewsletterSubscriber::class);
    }
}
