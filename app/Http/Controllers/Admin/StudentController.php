<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Student;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class StudentController extends Controller
{
    /**
     * 受講者一覧を表示
     */
    public function index(Request $request)
    {
        $query = Student::with('course')->orderBy('created_at', 'desc');
        
        // 検索フィルター
        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }
        
        // コースフィルター
        if ($request->has('course_id') && $request->input('course_id') !== 'all') {
            $query->where('course_id', $request->input('course_id'));
        }
        
        // ステータスフィルター
        if ($request->has('status') && $request->input('status') !== 'all') {
            $query->where('payment_status', $request->input('status'));
        }
        
        $students = $query->paginate(20);
        $courses = Course::all();
        
        return view('admin.students.index', compact('students', 'courses'));
    }

    /**
     * 受講者詳細を表示
     */
    public function show(Student $student)
    {
        return view('admin.students.show', compact('student'));
    }

    /**
     * 受講者編集フォームを表示
     */
    public function edit(Student $student)
    {
        $courses = Course::all();
        return view('admin.students.edit', compact('student', 'courses'));
    }

    /**
     * 受講者情報を更新
     */
    public function update(Request $request, Student $student)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'course_id' => 'required|exists:courses,id',
            'payment_status' => 'required|in:pending,completed,cancelled,refunded',
        ]);
        
        $student->update($validated);
        
        // コース変更の通知メールを送信
        if ($student->wasChanged('course_id')) {
            // ここでメール送信処理を実装
            // Mail::to($student->email)->send(new CourseChangedMail($student));
        }
        
        return redirect()->route('admin.students.show', $student)
            ->with('success', '受講者情報を更新しました。');
    }

    /**
     * 受講者を削除
     */
    public function destroy(Student $student)
    {
        $student->delete();
        
        return redirect()->route('admin.students.index')
            ->with('success', '受講者を削除しました。');
    }
}
