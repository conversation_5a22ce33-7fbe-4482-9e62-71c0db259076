<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Newsletter;
use App\Models\NewsletterSubscriber;
use App\Models\NewsletterLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Carbon\Carbon;

class NewsletterController extends Controller
{
    /**
     * メルマガ一覧を表示
     */
    public function index()
    {
        $newsletters = Newsletter::withCount(['subscribers' => function ($query) {
                $query->where('status', 'active');
            }])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // アクティブな購読者数を取得
        $activeSubscribersCount = NewsletterSubscriber::active()->count();

        return view('admin.newsletter.index', compact('newsletters', 'activeSubscribersCount'));
    }

    /**
     * メルマガ作成フォームを表示
     */
    public function create()
    {
        // フォームIDを生成（表示用）
        $formId = Str::random(10);
        return view('admin.newsletter.create', compact('formId'));
    }

    /**
     * メルマガを保存
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'gift_description' => 'nullable|string',
            'sales_letter' => 'nullable|string',
            'welcome_message' => 'nullable|string',
            'video_url' => 'nullable|url|max:255',
            'landing_page_url' => 'nullable|url|max:255',
            'form_id' => 'required|string|unique:newsletters,form_id',
        ]);

        // フォームデータを準備
        $newsletterData = [
            'title' => $validated['title'],
            'form_id' => $validated['form_id'],
            'gift_description' => $validated['gift_description'] ?? null,
            'sales_letter' => $validated['sales_letter'] ?? null,
            'welcome_message' => $validated['welcome_message'] ?? null,
            'video_url' => $validated['video_url'] ?? null,
            'landing_page_url' => $validated['landing_page_url'] ?? null,
            'is_active' => true,
            'content' => null,  // contentフィールドに明示的にnullを設定
        ];

        // アイキャッチ画像のアップロード処理
        if ($request->hasFile('image') && $request->file('image')->isValid()) {
            $imagePath = $request->file('image')->store('newsletter/images', 'public');
            $newsletterData['image_path'] = $imagePath;
        }

        // プレゼントファイルのアップロード処理
        if ($request->hasFile('gift_file') && $request->file('gift_file')->isValid()) {
            $giftPath = $request->file('gift_file')->store('newsletter/gifts', 'public');
            $newsletterData['gift_file_path'] = $giftPath;
        }

        // 動画ファイルのアップロード処理
        if ($request->hasFile('video_file') && $request->file('video_file')->isValid()) {
            $videoPath = $request->file('video_file')->store('newsletter/videos', 'public');
            $newsletterData['video_file_path'] = $videoPath;
        }

        // ニュースレターを作成
        $newsletter = Newsletter::create($newsletterData);

        return redirect()->route('admin.newsletter.index')
            ->with('success', 'メルマガを作成しました。');
    }

    /**
     * 購読者一覧を表示
     */
    public function subscribers(Request $request)
    {
        $query = NewsletterSubscriber::with('newsletter')->orderBy('created_at', 'desc');

        // 検索フィルター
        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // ステータスフィルター
        if ($request->has('status') && $request->input('status') !== 'all') {
            $query->where('status', $request->input('status'));
        }

        $subscribers = $query->paginate(20);
        $newsletters = Newsletter::orderBy('title')->get();

        return view('admin.newsletter.subscribers', compact('subscribers', 'newsletters'));
    }

    /**
     * メルマガ送信フォームを表示
     */
    public function showSendForm()
    {
        $newsletters = Newsletter::whereNull('sent_at')
            ->orderBy('created_at', 'desc')
            ->get();

        $subscribersCount = NewsletterSubscriber::active()->count();

        return view('admin.newsletter.send', compact('newsletters', 'subscribersCount'));
    }

    /**
     * メルマガを送信
     */
    public function send(Request $request)
    {
        $validated = $request->validate([
            'newsletter_id' => 'required|exists:newsletters,id',
        ]);

        $newsletter = Newsletter::findOrFail($validated['newsletter_id']);

        return $this->sendNewsletter($newsletter);
    }

    /**
     * メルマガ編集フォームを表示
     */
    public function edit(Newsletter $newsletter)
    {
        return view('admin.newsletter.edit', compact('newsletter'));
    }

    /**
     * メルマガを更新
     */
    public function update(Request $request, Newsletter $newsletter)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'gift_description' => 'nullable|string',
            'sales_letter' => 'nullable|string',
            'welcome_message' => 'nullable|string',
            'video_url' => 'nullable|url|max:255',
            'landing_page_url' => 'nullable|url|max:255',
            'is_active' => 'boolean',
        ]);

        // フォームデータを準備
        $newsletterData = [
            'title' => $validated['title'],
            'gift_description' => $validated['gift_description'] ?? null,
            'sales_letter' => $validated['sales_letter'] ?? null,
            'welcome_message' => $validated['welcome_message'] ?? null,
            'video_url' => $validated['video_url'] ?? null,
            'landing_page_url' => $validated['landing_page_url'] ?? null,
            'is_active' => $validated['is_active'] ?? $newsletter->is_active,
        ];

        // アイキャッチ画像のアップロード処理
        if ($request->hasFile('image') && $request->file('image')->isValid()) {
            // 既存の画像があれば削除
            if ($newsletter->image_path) {
                Storage::disk('public')->delete($newsletter->image_path);
            }
            $imagePath = $request->file('image')->store('newsletter/images', 'public');
            $newsletterData['image_path'] = $imagePath;
        }

        // プレゼントファイルのアップロード処理
        if ($request->hasFile('gift_file') && $request->file('gift_file')->isValid()) {
            // 既存のファイルがあれば削除
            if ($newsletter->gift_file_path) {
                Storage::disk('public')->delete($newsletter->gift_file_path);
            }
            $giftPath = $request->file('gift_file')->store('newsletter/gifts', 'public');
            $newsletterData['gift_file_path'] = $giftPath;
        }

        // 動画ファイルのアップロード処理
        if ($request->hasFile('video_file') && $request->file('video_file')->isValid()) {
            // 既存の動画ファイルがあれば削除
            if ($newsletter->video_file_path) {
                Storage::disk('public')->delete($newsletter->video_file_path);
            }
            $videoPath = $request->file('video_file')->store('newsletter/videos', 'public');
            $newsletterData['video_file_path'] = $videoPath;
        }

        // ニュースレターを更新
        $newsletter->update($newsletterData);

        return redirect()->route('admin.newsletter.index')
            ->with('success', 'メルマガを更新しました。');
    }

    /**
     * メルマガ送信ログを表示
     */
    public function logs()
    {
        $newsletters = Newsletter::whereNotNull('sent_at')
            ->orderBy('sent_at', 'desc')
            ->paginate(10);

        return view('admin.newsletter.logs', compact('newsletters'));
    }

    /**
     * 購読者を追加
     */
    public function storeSubscriber(Request $request)
    {
        $validated = $request->validate([
            'name' => 'nullable|string|max:255',
            'email' => 'required|email|max:255',
            'newsletter_id' => 'nullable|exists:newsletters,id',
        ]);

        // 既存の購読者かチェック
        $subscriber = NewsletterSubscriber::where('email', $validated['email'])->first();

        if ($subscriber) {
            return redirect()->route('admin.newsletter.subscribers')
                ->with('error', 'このメールアドレスは既に登録されています。');
        }

        // 購読者を作成
        NewsletterSubscriber::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'status' => 'active',
            'newsletter_id' => $validated['newsletter_id'],
        ]);

        // ニュースレターの購読者数を更新
        if (!empty($validated['newsletter_id'])) {
            $this->updateNewsletterSubscriberCount($validated['newsletter_id']);
        }

        return redirect()->route('admin.newsletter.subscribers')
            ->with('success', '購読者を追加しました。');
    }

    /**
     * 購読者を更新
     */
    public function updateSubscriber(Request $request, NewsletterSubscriber $subscriber)
    {
        $validated = $request->validate([
            'name' => 'nullable|string|max:255',
            'email' => 'required|email|max:255',
            'status' => 'required|in:active,unsubscribed',
            'newsletter_id' => 'nullable|exists:newsletters,id',
        ]);

        // メールアドレスの重複チェック（自分自身は除く）
        $existingSubscriber = NewsletterSubscriber::where('email', $validated['email'])
            ->where('id', '!=', $subscriber->id)
            ->first();

        if ($existingSubscriber) {
            return redirect()->route('admin.newsletter.subscribers')
                ->with('error', 'このメールアドレスは既に他の購読者に使用されています。');
        }

        // 購読解除日時の設定
        if ($validated['status'] === 'unsubscribed' && $subscriber->status === 'active') {
            $subscriber->unsubscribed_at = now();
        } elseif ($validated['status'] === 'active' && $subscriber->status === 'unsubscribed') {
            $subscriber->unsubscribed_at = null;
        }

        // 更新前のニュースレターIDを保存
        $oldNewsletterID = $subscriber->newsletter_id;

        // 購読者を更新
        $subscriber->update([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'status' => $validated['status'],
            'newsletter_id' => $validated['newsletter_id'],
        ]);

        // 古いニュースレターと新しいニュースレターの購読者数を更新
        if ($oldNewsletterID) {
            $this->updateNewsletterSubscriberCount($oldNewsletterID);
        }

        if (!empty($validated['newsletter_id']) && $validated['newsletter_id'] != $oldNewsletterID) {
            $this->updateNewsletterSubscriberCount($validated['newsletter_id']);
        }

        return redirect()->route('admin.newsletter.subscribers')
            ->with('success', '購読者情報を更新しました。');
    }

    /**
     * 購読者を削除
     */
    public function destroySubscriber(NewsletterSubscriber $subscriber)
    {
        // 削除前のニュースレターIDを保存
        $newsletterId = $subscriber->newsletter_id;

        $subscriber->delete();

        // ニュースレターの購読者数を更新
        if ($newsletterId) {
            $this->updateNewsletterSubscriberCount($newsletterId);
        }

        return redirect()->route('admin.newsletter.subscribers')
            ->with('success', '購読者を削除しました。');
    }

    /**
     * ニュースレターの購読者数を更新
     */
    private function updateNewsletterSubscriberCount($newsletterId)
    {
        if (!$newsletterId) {
            return;
        }

        $newsletter = Newsletter::find($newsletterId);
        if (!$newsletter) {
            return;
        }

        // アクティブな購読者数を取得して更新
        $activeSubscribersCount = NewsletterSubscriber::where('newsletter_id', $newsletterId)
            ->where('status', 'active')
            ->count();

        $newsletter->recipients_count = $activeSubscribersCount;
        $newsletter->save();
    }

    /**
     * メルマガを送信する処理
     */
    private function sendNewsletter(Newsletter $newsletter)
    {
        // アクティブな購読者を取得
        $subscribers = NewsletterSubscriber::active()->get();

        if ($subscribers->isEmpty()) {
            return redirect()->route('admin.newsletter.send')
                ->with('error', '送信先の購読者がいません。');
        }

        $successCount = 0;
        $failCount = 0;

        foreach ($subscribers as $subscriber) {
            try {
                // メール送信処理
                // Mail::to($subscriber->email)->send(new NewsletterMail($newsletter, $subscriber));

                // ログを記録
                NewsletterLog::create([
                    'newsletter_id' => $newsletter->id,
                    'recipient_email' => $subscriber->email,
                    'status' => 'sent',
                ]);

                $successCount++;
            } catch (\Exception $e) {
                // エラーログを記録
                NewsletterLog::create([
                    'newsletter_id' => $newsletter->id,
                    'recipient_email' => $subscriber->email,
                    'status' => 'failed',
                    'error_message' => $e->getMessage(),
                ]);

                Log::error('Newsletter sending failed', [
                    'newsletter_id' => $newsletter->id,
                    'subscriber_email' => $subscriber->email,
                    'error' => $e->getMessage(),
                ]);

                $failCount++;
            }
        }

        // ニュースレターの送信状態を更新
        $newsletter->sent_at = Carbon::now();
        $newsletter->recipients_count = $successCount;
        $newsletter->save();

        return redirect()->route('admin.newsletter.logs')
            ->with('success', "メルマガを送信しました。成功: {$successCount}件、失敗: {$failCount}件");
    }
}
