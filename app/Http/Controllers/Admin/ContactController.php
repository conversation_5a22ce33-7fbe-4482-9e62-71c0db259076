<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Mail\ContactReplyMail;
use App\Models\Contact;
use App\Models\ContactReply;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ContactController extends Controller
{
    /**
     * お問い合わせ一覧を表示
     */
    public function index(Request $request)
    {
        $query = Contact::orderBy('created_at', 'desc');

        // 検索フィルター
        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%");
            });
        }

        // ステータスフィルター
        if ($request->has('status') && $request->input('status') !== 'all') {
            $query->where('status', $request->input('status'));
        }

        // ソースフィルター
        if ($request->has('source') && $request->input('source') !== 'all') {
            $query->where('source', $request->input('source'));
        }

        // 日付範囲フィルター
        if ($request->has('date_from')) {
            $query->whereDate('created_at', '>=', $request->input('date_from'));
        }

        if ($request->has('date_to')) {
            $query->whereDate('created_at', '<=', $request->input('date_to'));
        }

        $contacts = $query->paginate(20);

        return view('admin.contacts.index', compact('contacts'));
    }

    /**
     * お問い合わせ詳細を表示
     */
    public function show(Contact $contact)
    {
        // 返信を取得
        $replies = $contact->replies()->orderBy('created_at', 'asc')->get();

        return view('admin.contacts.show', compact('contact', 'replies'));
    }

    /**
     * お問い合わせに返信
     */
    public function reply(Request $request, Contact $contact)
    {
        $validated = $request->validate([
            'message' => 'required|string',
        ]);

        try {
            // メール送信処理
            Mail::to($contact->email)->send(new ContactReplyMail($contact, $validated['message']));

            // 返信を記録
            $reply = ContactReply::create([
                'contact_id' => $contact->id,
                'message' => $validated['message'],
                'sent_at' => Carbon::now(),
                'status' => 'sent',
            ]);

            // お問い合わせのステータスを更新
            $contact->status = 'replied';
            $contact->save();

            return redirect()->route('admin.contacts.show', $contact)
                ->with('success', '返信を送信しました。');
        } catch (\Exception $e) {
            // エラーログを記録
            Log::error('Contact reply failed', [
                'contact_id' => $contact->id,
                'error' => $e->getMessage(),
            ]);

            // 失敗した返信を記録
            ContactReply::create([
                'contact_id' => $contact->id,
                'message' => $validated['message'],
                'status' => 'failed',
                'error_message' => $e->getMessage(),
            ]);

            return redirect()->route('admin.contacts.show', $contact)
                ->with('error', '返信の送信に失敗しました: ' . $e->getMessage());
        }
    }

    /**
     * お問い合わせを削除
     */
    public function destroy(Contact $contact)
    {
        $contact->delete();

        return redirect()->route('admin.contacts.index')
            ->with('success', 'お問い合わせを削除しました。');
    }

    /**
     * IMAPからメールを取得する処理
     * このメソッドはスケジュールタスクから呼び出すことを想定
     * また、管理画面からの手動実行も可能
     */
    public function fetchEmailsFromImap()
    {
        // 開発環境かどうかをチェック
        $isLocalEnvironment = app()->environment('local');

        if ($isLocalEnvironment) {
            // 開発環境では処理をスキップしてメッセージを表示
            if (request()->expectsJson()) {
                return response()->json(['info' => '開発環境ではIMAPメール取得機能は無効化されています。本番環境で動作します。']);
            } elseif (request()->isMethod('post')) {
                return redirect()->route('admin.contacts.index')
                    ->with('info', '開発環境ではIMAPメール取得機能は無効化されています。本番環境で動作します。');
            }
            return;
        }

        // IMAPの設定
        $host = env('MAIL_HOST');
        $port = env('IMAP_PORT', 993);
        $username = env('MAIL_USERNAME');
        $password = env('MAIL_PASSWORD');
        $encryption = env('IMAP_ENCRYPTION', 'ssl');

        // 取得したメール数をカウント
        $fetchedCount = 0;

        if (!$host || !$username || !$password) {
            Log::error('IMAP configuration missing');

            // 手動実行の場合はリダイレクト
            if (request()->expectsJson()) {
                return response()->json(['error' => 'IMAP設定が不足しています。'], 400);
            } elseif (request()->isMethod('post')) {
                return redirect()->route('admin.contacts.index')
                    ->with('error', 'IMAP設定が不足しています。環境変数を確認してください。');
            }

            return;
        }

        try {
            // IMAPサーバーに接続
            $mailbox = "{{$host}:{$port}/{$encryption}}INBOX";
            $inbox = imap_open($mailbox, $username, $password);

            if (!$inbox) {
                Log::error('IMAP connection failed: ' . imap_last_error());

                // 手動実行の場合はリダイレクト
                if (request()->expectsJson()) {
                    return response()->json(['error' => 'IMAPサーバーへの接続に失敗しました: ' . imap_last_error()], 500);
                } elseif (request()->isMethod('post')) {
                    return redirect()->route('admin.contacts.index')
                        ->with('error', 'IMAPサーバーへの接続に失敗しました: ' . imap_last_error());
                }

                return;
            }

            // 未読メールを検索
            $emails = imap_search($inbox, 'UNSEEN');

            if ($emails) {
                foreach ($emails as $email_number) {
                    // メールヘッダーを取得
                    $header = imap_headerinfo($inbox, $email_number);

                    // メッセージIDを取得
                    $message_id = $header->message_id;

                    // 既存のお問い合わせをチェック
                    $existingContact = Contact::where('message_id', $message_id)->first();
                    if ($existingContact) {
                        continue; // 既に処理済みのメールはスキップ
                    }

                    // 送信者情報を取得
                    $from = $header->from[0];
                    $fromName = $from->personal ?? $from->mailbox;
                    $fromEmail = $from->mailbox . '@' . $from->host;

                    // 件名を取得
                    $subject = $header->subject;

                    // 本文を取得
                    $message = imap_fetchbody($inbox, $email_number, 1);

                    // エンコーディングを処理
                    if ($header->encoding == 3) { // BASE64
                        $message = base64_decode($message);
                    } elseif ($header->encoding == 4) { // QUOTED-PRINTABLE
                        $message = quoted_printable_decode($message);
                    }

                    // お問い合わせとして保存
                    Contact::create([
                        'name' => $fromName,
                        'email' => $fromEmail,
                        'subject' => $subject,
                        'message' => $message,
                        'source' => 'imap',
                        'status' => 'new',
                        'message_id' => $message_id,
                    ]);

                    $fetchedCount++;
                }
            }

            // 接続を閉じる
            imap_close($inbox);

            // 手動実行の場合はリダイレクト
            if (request()->expectsJson()) {
                return response()->json(['success' => true, 'count' => $fetchedCount]);
            } elseif (request()->isMethod('post')) {
                if ($fetchedCount > 0) {
                    return redirect()->route('admin.contacts.index')
                        ->with('success', $fetchedCount . '件のメールを取得しました。');
                } else {
                    return redirect()->route('admin.contacts.index')
                        ->with('info', '新しいメールはありませんでした。');
                }
            }

        } catch (\Exception $e) {
            Log::error('IMAP fetch error: ' . $e->getMessage());

            // 手動実行の場合はリダイレクト
            if (request()->expectsJson()) {
                return response()->json(['error' => 'メール取得中にエラーが発生しました: ' . $e->getMessage()], 500);
            } elseif (request()->isMethod('post')) {
                return redirect()->route('admin.contacts.index')
                    ->with('error', 'メール取得中にエラーが発生しました: ' . $e->getMessage());
            }
        }
    }
}
