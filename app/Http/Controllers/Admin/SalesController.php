<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\FincodeApiCall;
use App\Models\FincodePayment;
use App\Models\Student;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SalesController extends Controller
{
    // Fincode APIのベースURL
    private $apiBaseUrl;

    // Fincode APIのヘッダー
    private $apiHeaders;

    /**
     * コンストラクタ
     */
    public function __construct()
    {
        // 環境に応じてAPIのURLを設定
        $environment = env('FINCODE_ENVIRONMENT', 'test');
        $this->apiBaseUrl = $environment === 'production'
            ? 'https://api.fincode.jp'
            : 'https://api.test.fincode.jp';

        // APIヘッダーを設定 - API_SECRETを使用
        $this->apiHeaders = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . env('FINCODE_API_SECRET'),
        ];
    }

    /**
     * 売上一覧を表示
     */
    public function index(Request $request)
    {
        // ページが開かれた時に、1時間以上前の最後のAPI呼び出しがある場合は自動的に更新
        if (FincodeApiCall::isCallNeeded('sales')) {
            // FincodePaymentモデルを使用して決済データを取得・保存
            FincodePayment::fetchAndStoreFromFincode();
        }

        // Fincode決済データを取得（すべてのデータをJSでフィルタリングするため）
        $sales = FincodePayment::with('student')->orderBy('process_date', 'desc')->get();

        // 合計金額を計算
        $totalAmount = FincodePayment::sum('amount');

        // 最後のAPI呼び出し情報を取得
        $lastApiCall = FincodeApiCall::getLastCall('sales');

        return view('admin.sales.index', compact('sales', 'totalAmount', 'lastApiCall'));
    }



    /**
     * 売上詳細を表示
     */
    public function show(Student $student)
    {
        // 決済情報をFincodeから取得
        $paymentDetails = null;

        if ($student->payment_id && $student->payment_status === 'completed') {
            try {
                // 決済情報を取得するエンドポイント
                $endpoint = '/v1/payments/' . $student->payment_id;
                $response = Http::withHeaders($this->apiHeaders)
                    ->get($this->apiBaseUrl . $endpoint);

                if ($response->successful()) {
                    $paymentDetails = $response->json();
                } else {
                    Log::error('Fincode payment retrieval failed', [
                        'student_id' => $student->id,
                        'payment_id' => $student->payment_id,
                        'response' => $response->body(),
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('Fincode API error', [
                    'message' => $e->getMessage(),
                    'student_id' => $student->id,
                ]);
            }
        }

        return view('admin.sales.show', compact('student', 'paymentDetails'));
    }

    /**
     * 売上を削除
     */
    public function destroy(Student $student)
    {
        // 決済が完了している場合は、Fincodeで返金処理を行う
        if ($student->payment_status === 'completed' && $student->payment_id) {
            try {
                // 返金処理を行うエンドポイント
                $endpoint = '/v1/payments/' . $student->payment_id . '/refund';
                $response = Http::withHeaders($this->apiHeaders)
                    ->put($this->apiBaseUrl . $endpoint, [
                        'amount' => $student->amount,
                        'job_code' => 'REFUND',
                    ]);

                if ($response->successful()) {
                    $student->payment_status = 'refunded';
                    $student->save();

                    return redirect()->route('admin.sales.index')
                        ->with('success', '返金処理が完了しました。');
                } else {
                    Log::error('Fincode refund failed', [
                        'student_id' => $student->id,
                        'payment_id' => $student->payment_id,
                        'response' => $response->body(),
                    ]);

                    return redirect()->route('admin.sales.show', $student)
                        ->with('error', '返金処理に失敗しました。Fincodeの管理画面で確認してください。');
                }
            } catch (\Exception $e) {
                Log::error('Fincode API error', [
                    'message' => $e->getMessage(),
                    'student_id' => $student->id,
                ]);

                return redirect()->route('admin.sales.show', $student)
                    ->with('error', 'APIエラーが発生しました: ' . $e->getMessage());
            }
        }

        // 決済が完了していない場合は、単純に削除
        $student->delete();

        return redirect()->route('admin.sales.index')
            ->with('success', '売上データを削除しました。');
    }

    /**
     * Fincodeから直接決済データを取得して保存し、売上一覧ページにリダイレクト
     */
    public function fincodePayments(Request $request)
    {
        // 強制的にAPIを呼び出して決済データを更新
        $result = FincodePayment::fetchAndStoreFromFincode([], true);

        if ($result['success']) {
            return redirect()->route('admin.sales.index')
                ->with('success', $result['message']);
        } else {
            return redirect()->route('admin.sales.index')
                ->with('error', $result['message']);
        }
    }

    /**
     * 決済を返金処理する
     */
    public function refund(Request $request, $paymentId)
    {
        // 決済情報を取得
        $payment = FincodePayment::where('payment_id', $paymentId)->first();

        if (!$payment) {
            return redirect()->route('admin.sales.index')
                ->with('error', '決済情報が見つかりませんでした。');
        }

        // 決済が確定済みかチェック
        if ($payment->status !== 'CAPTURED') {
            return redirect()->route('admin.sales.index')
                ->with('error', '売上確定済みの決済のみ返金できます。');
        }

        try {
            // 返金処理を行うエンドポイント
            $endpoint = '/v1/payments/' . $payment->payment_id . '/cancel';

            // APIヘッダーを設定
            $headers = [
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . env('FINCODE_API_SECRET'),
            ];

            // リクエストデータを設定
            $data = [
                'pay_type' => $payment->pay_type,
                'access_id' => $payment->access_id,
            ];

            // APIリクエストを実行
            $response = Http::withHeaders($headers)
                ->put(($this->apiBaseUrl . $endpoint), $data);

            if ($response->successful()) {
                // 決済ステータスを更新
                $payment->status = 'CANCELED';
                $payment->save();

                // 関連する学生情報があれば更新
                if ($payment->student) {
                    $payment->student->payment_status = 'refunded';
                    $payment->student->save();
                }

                return redirect()->route('admin.sales.index')
                    ->with('success', '返金処理が完了しました。');
            } else {
                Log::error('Fincode refund failed', [
                    'payment_id' => $payment->payment_id,
                    'response' => $response->body(),
                ]);

                return redirect()->route('admin.sales.index')
                    ->with('error', '返金処理に失敗しました。Fincodeの管理画面で確認してください。');
            }
        } catch (\Exception $e) {
            Log::error('Fincode API error', [
                'message' => $e->getMessage(),
                'payment_id' => $payment->payment_id,
            ]);

            return redirect()->route('admin.sales.index')
                ->with('error', 'APIエラーが発生しました: ' . $e->getMessage());
        }
    }
}
