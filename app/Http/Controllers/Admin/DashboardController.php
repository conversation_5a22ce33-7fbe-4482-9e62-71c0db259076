<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Student;
use App\Models\NewsletterSubscriber;
use App\Models\Contact;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * 管理者ダッシュボードを表示
     */
    public function index()
    {
        // 売上状況
        $totalSales = Student::where('payment_status', 'completed')->sum('amount');
        $monthlySales = Student::where('payment_status', 'completed')
            ->whereMonth('created_at', Carbon::now()->month)
            ->sum('amount');
        
        // 生徒加入数
        $totalStudents = Student::where('payment_status', 'completed')->count();
        $monthlyStudents = Student::where('payment_status', 'completed')
            ->whereMonth('created_at', Carbon::now()->month)
            ->count();
        
        // メルマガ購読者数
        $totalSubscribers = NewsletterSubscriber::active()->count();
        $monthlySubscribers = NewsletterSubscriber::active()
            ->whereMonth('created_at', Carbon::now()->month)
            ->count();
        
        // 月別売上グラフ用データ
        $monthlySalesData = Student::where('payment_status', 'completed')
            ->select(
                DB::raw('SUM(amount) as total'),
                DB::raw('MONTH(created_at) as month')
            )
            ->whereYear('created_at', Carbon::now()->year)
            ->groupBy('month')
            ->get()
            ->pluck('total', 'month')
            ->toArray();
        
        // 月別生徒加入数グラフ用データ
        $monthlyStudentsData = Student::where('payment_status', 'completed')
            ->select(
                DB::raw('COUNT(*) as count'),
                DB::raw('MONTH(created_at) as month')
            )
            ->whereYear('created_at', Carbon::now()->year)
            ->groupBy('month')
            ->get()
            ->pluck('count', 'month')
            ->toArray();
        
        // 月別メルマガ購読者増減グラフ用データ
        $monthlySubscribersData = NewsletterSubscriber::active()
            ->select(
                DB::raw('COUNT(*) as count'),
                DB::raw('MONTH(created_at) as month')
            )
            ->whereYear('created_at', Carbon::now()->year)
            ->groupBy('month')
            ->get()
            ->pluck('count', 'month')
            ->toArray();
        
        // 最近のお問い合わせ
        $recentContacts = Contact::orderBy('created_at', 'desc')
            ->take(5)
            ->get();
        
        return view('admin.dashboard', compact(
            'totalSales',
            'monthlySales',
            'totalStudents',
            'monthlyStudents',
            'totalSubscribers',
            'monthlySubscribers',
            'monthlySalesData',
            'monthlyStudentsData',
            'monthlySubscribersData',
            'recentContacts'
        ));
    }
}
