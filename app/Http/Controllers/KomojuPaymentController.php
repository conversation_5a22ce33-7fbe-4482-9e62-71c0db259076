<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Student;
use App\Models\KomojuPayment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use App\Mail\PurchaseCompleteMail;

class KomojuPaymentController extends Controller
{
    // KOMOJUのAPIベースURL
    private $apiBaseUrl = 'https://komoju.com/api/v1';

    // KOMOJUのAPIヘッダー
    private $apiHeaders;

    /**
     * コンストラクタ
     */
    public function __construct()
    {
        // APIヘッダーを設定
        $this->apiHeaders = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'Authorization' => 'Basic ' . base64_encode(env('API_SECRET', '') . ':'),
        ];

        // APIキーのログ出力（デバッグ用、本番環境では削除してください）
        Log::debug('KOMOJU API設定', [
            'API_SECRET' => env('API_SECRET') ? 'セットされています' : 'セットされていません',
            'API_PUBLIC' => env('API_PUBLIC') ? 'セットされています' : 'セットされていません',
            'UUID' => env('UUID') ? 'セットされています' : 'セットされていません',
        ]);
    }

    /**
     * 購入者情報入力ページを表示
     */
    public function showCustomerInfoForm(Request $request)
    {
        // リクエストからコースIDを取得
        $courseId = $request->input('course_id');

        if (!$courseId) {
            return redirect()->route('landing')->with('error', 'コースIDが指定されていません');
        }

        // コース情報を取得
        $course = Course::find($courseId);

        if (!$course) {
            return redirect()->route('landing')->with('error', '指定されたコースが見つかりません');
        }

        return view('payment.customer_info', compact('course'));
    }

    /**
     * 購入者情報を保存し、決済ページにリダイレクト
     */
    public function storeCustomerInfo(Request $request)
    {
        // バリデーション
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'course_id' => 'required|exists:courses,id',
            'terms' => 'required',
        ], [
            'name.required' => 'お名前は必須です',
            'email.required' => 'メールアドレスは必須です',
            'email.email' => '有効なメールアドレスを入力してください',
            'course_id.required' => 'コースIDが指定されていません',
            'course_id.exists' => '指定されたコースが見つかりません',
            'terms.required' => '利用規約とプライバシーポリシーへの同意が必要です',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // コース情報を取得
        $course = Course::find($request->course_id);

        // 注文IDを生成
        $orderId = uniqid('order_');

        // 購入者情報をデータベースに保存
        try {
            $student = Student::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'order_id' => $orderId,
                'course_id' => $request->course_id,
                'payment_status' => 'pending',
                'payment_type' => 'Card', // デフォルトはカード決済
                'amount' => $course->price,
            ]);

            // 決済ページへリダイレクト
            return $this->redirectToPayment($student);

        } catch (\Exception $e) {
            Log::error('購入者情報保存エラー', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return redirect()->back()->with('error', '購入者情報の保存中にエラーが発生しました。もう一度お試しください。')->withInput();
        }
    }

    /**
     * KOMOJU決済ページにリダイレクト
     *
     * @param Student $student 購入者情報
     * @return \Illuminate\Http\RedirectResponse
     */
    private function redirectToPayment(Student $student)
    {
        // 決済セッション作成APIのエンドポイント
        $endpoint = $this->apiBaseUrl . '/sessions';

        // 決済セッション作成APIのリクエストデータ
        $data = [
            'amount' => (string)$student->amount, // 文字列に変換
            'currency' => 'JPY',
            'mode' => 'payment',
            'return_url' => url('/payment/success') . '?id=' . urlencode($student->order_id),
            'cancel_url' => url('/payment/cancel') . '?id=' . urlencode($student->order_id),
            'external_order_num' => $student->order_id,
            'metadata' => [
                'name' => $student->name,
                'email' => $student->email,
                'course_id' => (string)$student->course_id, // 文字列に変換
                'student_id' => (string)$student->id // 文字列に変換
            ]
        ];

        try {
            // KOMOJUのAPIを呼び出して決済セッションを作成
            $response = Http::withHeaders($this->apiHeaders)
                ->post($endpoint, $data);

            // レスポンスをログに記録
            Log::info('KOMOJU 決済セッション作成 レスポンス', [
                'response' => $response->json(),
            ]);

            // レスポンスデータを取得
            $responseData = $response->json();

            // 決済情報をデータベースに保存
            if ($response->successful() && isset($responseData['id'])) {
                // セッション情報をデータベースに保存
                $paymentData = array_merge($responseData, [
                    'external_order_num' => $student->order_id,
                    'metadata' => [
                        'name' => $student->name,
                        'email' => $student->email,
                        'student_id' => (string)$student->id,
                        'course_id' => (string)$student->course_id
                    ]
                ]);

                KomojuPayment::storePaymentData($paymentData);
            }

            // エラーレスポンスの確認
            if ($response->failed()) {
                Log::error('KOMOJU 決済セッション作成 エラー', [
                    'response' => $response->json(),
                ]);
                return redirect()->route('payment.customer_info', ['course_id' => $student->course_id])
                    ->with('error', '決済処理の準備中にエラーが発生しました。もう一度お試しください。');
            }

            // レスポンスから決済URLを取得
            $paymentUrl = $responseData['session_url'] ?? null;

            if (!$paymentUrl) {
                Log::error('KOMOJU 決済URL取得 エラー', [
                    'response' => $responseData,
                ]);
                return redirect()->route('payment.customer_info', ['course_id' => $student->course_id])
                    ->with('error', '決済URLの取得に失敗しました。もう一度お試しください。');
            }

            // 決済ページに直接リダイレクト
            return redirect()->away($paymentUrl);

        } catch (\Exception $e) {
            Log::error('KOMOJU API エラー', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return redirect()->route('payment.customer_info', ['course_id' => $student->course_id])
                ->with('error', '決済処理の準備中にエラーが発生しました。もう一度お試しください。');
        }
    }

    /**
     * 支払い成功ページ
     */
    public function success(Request $request)
    {
        // API経由のリクエストかどうかを判定
        $isApiRequest = $request->is('api/*');

        // セッションIDを取得
        $sessionId = $request->input('session_id');
        $orderId = $request->input('id');

        Log::info('KOMOJU 決済成功', [
            'request' => $request->all(),
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'isApiRequest' => $isApiRequest,
            'referer' => $request->header('referer'),
        ]);

        // 受講者情報を取得
        $student = Student::where('order_id', $orderId)->first();

        if (!$student) {
            Log::error('KOMOJU 決済成功: 受講者情報が見つかりません', [
                'order_id' => $orderId,
            ]);
            if ($isApiRequest) {
                return response()->json([
                    'success' => false,
                    'message' => '購入者情報が見つかりませんでした',
                ], 404);
            } else {
                return redirect()->route('landing')->with('error', '受講者情報が見つかりません');
            }
        }

        // 決済状態の確認
        // セッションIDがなく、かつ決済が完了していない場合は、戻るボタンで戻ってきた可能性がある
        if (!$sessionId && $student->payment_status !== 'completed') {
            // 決済ステータスパラメータの確認
            $status = $request->input('status');

            Log::info('KOMOJU 決済: セッションIDなしでアクセス', [
                'order_id' => $orderId,
                'status' => $status,
                'payment_status' => $student->payment_status
            ]);

            // 明示的なキャンセルパラメータがある場合
            if ($status === 'cancelled' || $request->has('cancelled')) {
                Log::info('KOMOJU 決済: 明示的なキャンセル', [
                    'order_id' => $orderId,
                    'status' => $status
                ]);

                // 決済が未完了の状態に戻す
                if ($student->payment_status !== 'pending') {
                    $student->payment_status = 'pending';
                    $student->save();
                }

                return redirect()->route('landing')->with('payment_incomplete', '決済が完了していません。もう一度お試しください。');
            }

            // 決済がまだ完了していない場合は未完了状態のままにする
            // ステータスは変更せず、ログだけ残す
            Log::info('KOMOJU 決済: 決済が完了せず', [
                'order_id' => $orderId,
                'student_id' => $student->id,
                'payment_status' => $student->payment_status
            ]);

            return redirect()->route('landing')->with('payment_incomplete', '決済が完了していません。もう一度お試しください。');
        }

        // 通知処理
		$message = 'Tarotique売上: ' . number_format($student->amount) . '円'.' ID:'.$student->order_id;
		$response = Http::post('https://api.9diz.com/api.php', [
			'secret' => 'でびきんinfo',
			'message' => $message,
		]);

        // セッション情報を取得して決済状態を確認
        if ($sessionId) {
            $this->verifyPaymentSession($sessionId, $student);

            // セッション検証後に決済状態を再確認
            $student = Student::where('order_id', $orderId)->first();

            // 決済が完了していない場合はキャンセル扱いにする
            if ($student->payment_status !== 'completed') {
                Log::warning('KOMOJU 決済: セッション検証後も決済が完了していません', [
                    'order_id' => $orderId,
                    'payment_status' => $student->payment_status,
                ]);

                return redirect()->route('landing')->with('payment_incomplete', '決済処理が完了しませんでした。もう一度お試しください。');
            }
        } else {
            // セッションIDがない場合は、KOMOJUからの正規のコールバックかどうかを確認
            // 正規のコールバックの場合のみ決済完了とする
            $status = $request->input('status');

            // 明示的な成功ステータスがある場合
            if ($status === 'completed' || $isApiRequest) {
                $student->payment_status = 'completed';
                $student->save();

                Log::info('KOMOJU 決済: セッションIDなしで決済完了', [
                    'order_id' => $orderId,
                    'is_api_request' => $isApiRequest,
                    'status' => $status
                ]);
            } else {
                Log::warning('KOMOJU 決済: 決済状態が不明です', [
                    'order_id' => $orderId,
                    'is_api_request' => $isApiRequest,
                    'status' => $status,
                    'request_all' => $request->all()
                ]);

                // 決済がすでに完了している場合は完了ページを表示
                if ($student->payment_status === 'completed') {
                    return view('payment.complete', compact('student'));
                }

                // それ以外の場合は未完了状態に戻す
                if ($student->payment_status !== 'pending') {
                    $student->payment_status = 'pending';
                    $student->save();
                }

                return redirect()->route('landing')->with('payment_incomplete', '決済処理が正しく行われませんでした。もう一度お試しください。');
            }
        }

        // ユーザーが認証済みの場合、コースへのアクセス権を付与
        if (auth()->check()) {
            $user = auth()->user();

            // ユーザーがまだこのコースを持っていない場合のみ追加
            if (!$user->courses()->where('course_id', $student->course_id)->exists()) {
                $user->courses()->attach($student->course_id);
            }
        }

        // 成功時のレスポンス
        if ($isApiRequest) {
            // APIリクエストの場合はリダイレクト用のページを表示
            // JavaScriptが自動的にリダイレクトを処理する
            $responseData = [
                'success' => true,
                'message' => '決済が完了しました',
                'order_id' => $student->order_id,
                'payment_id' => $student->payment_id,
                'redirect_url' => route('payment.success') . '?id=' . urlencode($student->order_id),
            ];

            // レスポンスデータをログに記録
            Log::info('KOMOJU 決済成功レスポンス', [
                'responseData' => $responseData,
            ]);

            // リダイレクト用のページを返す
            return view('payment.api-response', ['data' => $responseData]);
        } else {
            // 購入完了ページを表示
            return view('payment.complete', compact('student'));
        }
    }

    /**
     * 支払いキャンセルページ
     */
    public function cancel(Request $request)
    {
        // API経由のリクエストかどうかを判定
        $isApiRequest = $request->is('api/*');

        // キャンセル情報をログに記録
        Log::info('KOMOJU 決済キャンセル', [
            'request' => $request->all(),
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'isApiRequest' => $isApiRequest,
        ]);

        $orderId = $request->input('id');
        $status = $request->input('status');

        // 受講者情報があればステータスを更新
        if ($orderId) {
            $student = Student::where('order_id', $orderId)->first();
            if ($student) {
                // すでに完了している場合は完了ページを表示
                if ($student->payment_status === 'completed') {
                    Log::warning('KOMOJU 決済キャンセル: すでに完了している決済のキャンセル要求', [
                        'order_id' => $orderId,
                        'student_id' => $student->id,
                        'payment_status' => $student->payment_status
                    ]);

                    if ($isApiRequest) {
                        return response()->json([
                            'success' => false,
                            'message' => '決済はすでに完了しています',
                            'order_id' => $orderId,
                        ], 400);
                    } else {
                        return view('payment.complete', compact('student'));
                    }
                }

                // 決済が未完了の状態に戻す
                if ($student->payment_status !== 'pending') {
                    $student->payment_status = 'pending';
                    $student->save();
                }

                Log::info('KOMOJU 決済: 決済が完了せず', [
                    'order_id' => $orderId,
                    'student_id' => $student->id,
                    'payment_status' => $student->payment_status,
                    'status_param' => $status
                ]);
            } else {
                Log::error('KOMOJU 決済キャンセル: 受講者情報が見つかりません', [
                    'order_id' => $orderId,
                ]);
            }
        } else {
            Log::error('KOMOJU 決済キャンセル: order_idが指定されていません', [
                'request' => $request->all(),
            ]);
        }

        // レスポンスを返す
        if ($isApiRequest) {
            return response()->json([
                'success' => true,
                'message' => '決済が完了していません',
                'order_id' => $orderId,
                'redirect_url' => route('payment.cancel') . '?id=' . urlencode($orderId),
            ], 200);
        } else {
            // トップページにリダイレクトし、メッセージを表示
            return redirect()->route('landing')->with('payment_incomplete', '決済が完了していません。もう一度お試しください。');
        }
    }

    /**
     * 決済セッションを検証して決済状態を更新
     */
    private function verifyPaymentSession($sessionId, Student $student)
    {
        // セッション情報取得APIのエンドポイント
        $endpoint = $this->apiBaseUrl . '/sessions/' . $sessionId;

        try {
            // KOMOJUのAPIを呼び出してセッション情報を取得
            $response = Http::withHeaders($this->apiHeaders)
                ->get($endpoint);

            // レスポンスをログに記録
            Log::info('KOMOJU セッション検証 レスポンス', [
                'response' => $response->json(),
            ]);

            // エラーレスポンスの確認
            if ($response->failed()) {
                Log::error('KOMOJU セッション検証 エラー', [
                    'response' => $response->json(),
                ]);
                return;
            }

            // レスポンスから決済状態を取得
            $responseData = $response->json();
            $status = $responseData['status'] ?? null;

            // payment_idはpaymentオブジェクト内にある場合がある
            if (!empty($responseData['payment']) && !empty($responseData['payment']['id'])) {
                $paymentId = $responseData['payment']['id'];
            } else {
                $paymentId = $responseData['payment_id'] ?? null;
            }

            // 決済情報をデータベースに保存
            if ($response->successful()) {
                // セッション情報をデータベースに保存
                $paymentData = array_merge($responseData, [
                    'external_order_num' => $student->order_id,
                    'metadata' => [
                        'name' => $student->name,
                        'email' => $student->email,
                        'student_id' => (string)$student->id,
                        'course_id' => (string)$student->course_id
                    ]
                ]);

                KomojuPayment::storePaymentData($paymentData);
            }

            // 決済情報を更新
            $student->payment_id = $paymentId;
            $student->payment_status = $status === 'completed' ? 'completed' : 'pending';
            $student->payment_response = json_encode([
                'payment_info' => $responseData,
                'timestamp' => now()->toDateTimeString(),
            ]);
            $student->save();

        } catch (\Exception $e) {
            Log::error('KOMOJU セッション検証 エラー', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
}
