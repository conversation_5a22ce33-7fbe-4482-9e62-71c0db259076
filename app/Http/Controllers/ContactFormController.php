<?php

namespace App\Http\Controllers;

use App\Models\Contact;
use App\Services\LineMessagingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ContactFormController extends Controller
{
    /**
     * お問い合わせフォームを表示
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        return view('contact.index');
    }

    /**
     * お問い合わせを保存
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // バリデーション
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ], [
            'name.required' => 'お名前は必須です。',
            'email.required' => 'メールアドレスは必須です。',
            'email.email' => '有効なメールアドレスを入力してください。',
            'subject.required' => '件名は必須です。',
            'message.required' => 'メッセージは必須です。',
        ]);

        try {
            // お問い合わせを保存
            $contact = Contact::create([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'subject' => $validated['subject'],
                'message' => $validated['message'],
                'source' => 'form',
                'status' => 'new',
            ]);

            // LINE通知を送信
            try {
                $lineService = new LineMessagingService();
                $lineMessage = "Tarotiqueお問い合わせがありました。\n件名: {$validated['subject']}\n名前: {$validated['name']}\nメール: {$validated['email']}";
                $lineService->sendMessage($lineMessage);
            } catch (\Exception $e) {
                // LINE通知の送信に失敗しても処理は続行
                Log::error('LINE通知の送信に失敗しました。', [
                    'contact_id' => $contact->id,
                    'error' => $e->getMessage(),
                ]);
            }

            // 完了ページにリダイレクト
            return redirect()->route('contact.complete');
        } catch (\Exception $e) {
            // エラーログを記録
            Log::error('Contact form submission failed', [
                'error' => $e->getMessage(),
                'data' => $validated,
            ]);

            // エラーメッセージと共に元のページに戻る
            return back()->withInput()->with('error', 'お問い合わせの送信に失敗しました。しばらく経ってから再度お試しください。');
        }
    }

    /**
     * 送信完了ページを表示
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function complete()
    {
        return view('contact.complete');
    }
}
