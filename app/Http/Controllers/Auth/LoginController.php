<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Support\Facades\Auth;
use Lara<PERSON>\Socialite\Facades\Socialite;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/home';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except(['logout', 'handleGoogleCallback']);
        $this->middleware('auth')->only('logout');
    }

    /**
     * Redirect the user to the Google authentication page.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function redirectToGoogle()
    {
        // デバッグ情報をログに記録
        \Log::channel('single')->info('Redirecting to Google');

        // スコープを明示的に設定して、毎回認証画面が表示されるようにする
        return Socialite::driver('google')
            ->scopes(['profile', 'email'])
            ->with(['prompt' => 'select_account'])
            ->stateless()
            ->redirect();
    }

    /**
     * Obtain the user information from Google.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function handleGoogleCallback()
    {
        try {
            // デバッグ情報をログに記録
            \Log::channel('single')->info('Google callback started');

            // リクエストパラメータをログに記録
            \Log::channel('single')->info('Request parameters', ['params' => request()->all()]);

            $googleUser = Socialite::driver('google')->stateless()->user();
            \Log::channel('single')->info('Google user retrieved', ['id' => $googleUser->id, 'email' => $googleUser->email]);

            $user = User::where('google_id', $googleUser->id)->first();
            \Log::channel('single')->info('User search by google_id', ['found' => (bool)$user]);

            if (!$user) {
                // Check if user with same email exists
                $user = User::where('email', $googleUser->email)->first();
                \Log::channel('single')->info('User search by email', ['found' => (bool)$user]);

                if (!$user) {
                    // Create new user
                    \Log::channel('single')->info('Creating new user');
                    $user = User::create([
                        'name' => $googleUser->name,
                        'email' => $googleUser->email,
                        'password' => bcrypt(\Illuminate\Support\Str::random(16)),
                        'google_id' => $googleUser->id,
                        'avatar' => $googleUser->avatar,
                        'google_token' => $googleUser->token,
                        'google_refresh_token' => $googleUser->refreshToken ?? null,
                    ]);
                    \Log::channel('single')->info('New user created', ['id' => $user->id]);
                } else {
                    // Update existing user with Google info
                    \Log::channel('single')->info('Updating existing user', ['id' => $user->id]);
                    $user->update([
                        'google_id' => $googleUser->id,
                        'avatar' => $googleUser->avatar,
                        'google_token' => $googleUser->token,
                        'google_refresh_token' => $googleUser->refreshToken ?? null,
                    ]);
                    \Log::channel('single')->info('User updated');
                }
            }

            // セッションを再生成
            \Log::channel('single')->info('Regenerating session');
            request()->session()->regenerate();

            // ユーザーをログイン
            \Log::channel('single')->info('Logging in user', ['id' => $user->id]);
            Auth::login($user, true); // rememberパラメータをtrueに設定

            // セッションを保存
            \Log::channel('single')->info('Saving session');
            request()->session()->save();

            // セッションにユーザーIDを明示的に保存
            \Log::channel('single')->info('Storing user ID in session');
            request()->session()->put('auth.id', $user->id);
            request()->session()->save();

            \Log::channel('single')->info('Redirecting to ' . $this->redirectTo);
            return redirect($this->redirectTo);
        } catch (\Exception $e) {
            // エラーの詳細をログに記録
            \Log::channel('single')->error('Google authentication error: ' . $e->getMessage());
            \Log::channel('single')->error('Exception trace: ' . $e->getTraceAsString());
            return redirect('/login')->with('error', 'Google認証に失敗しました。もう一度お試しください。');
        }
    }
}
