<?php

namespace App\Http\Controllers;

use App\Models\Newsletter;
use App\Models\NewsletterSubscriber;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class NewsletterSubscriptionController extends Controller
{
    /**
     * ニュースレターのランディングページを表示
     */
    public function showLandingPage($formId)
    {
        // フォームIDからニュースレターを取得
        $newsletter = Newsletter::where('form_id', $formId)
            ->where('is_active', true)
            ->firstOrFail();

        // 外部ランディングページURLがあればリダイレクト
        if ($newsletter->landing_page_url) {
            return redirect()->away($newsletter->landing_page_url);
        }

        return view('newsletter.landing', compact('newsletter', 'formId'));
    }

    /**
     * 購読者登録フォームを表示
     */
    public function showSubscriptionForm($formId)
    {
        // フォームIDからニュースレターを取得
        $newsletter = Newsletter::where('form_id', $formId)
            ->where('is_active', true)
            ->firstOrFail();

        return view('newsletter.subscribe', compact('newsletter', 'formId'));
    }

    /**
     * 購読者を登録
     */
    public function subscribe(Request $request, $formId)
    {
        // フォームIDからニュースレターを取得
        $newsletter = Newsletter::where('form_id', $formId)
            ->where('is_active', true)
            ->firstOrFail();

        // バリデーション
        $validated = $request->validate([
            'email' => 'required|email|max:255',
            'name' => 'required|string|max:255',
        ]);

        // 既存の購読者かチェック
        $subscriber = NewsletterSubscriber::where('email', $validated['email'])->first();

        // 新規購読者か、購読解除済みの再購読かを記録
        $isNewSubscription = false;

        if (!$subscriber) {
            // 新規購読者を作成
            $subscriber = NewsletterSubscriber::create([
                'email' => $validated['email'],
                'name' => $validated['name'],
                'status' => 'active',
                'newsletter_id' => $newsletter->id,
            ]);
            $isNewSubscription = true;
        } elseif ($subscriber->status === 'unsubscribed') {
            // 購読解除済みの場合は再購読として処理
            $subscriber->status = 'active';
            $subscriber->unsubscribed_at = null;
            $subscriber->newsletter_id = $newsletter->id;
            $subscriber->save();
            $isNewSubscription = true;
        }

        // プレゼントファイルのURLを生成
        $giftUrl = null;
        if ($newsletter->gift_file_path) {
            $giftUrl = asset('storage/' . $newsletter->gift_file_path);
        }

        // メール送信処理
        try {
            Mail::to($subscriber->email)->send(new \App\Mail\NewsletterWelcome($subscriber, $newsletter, $giftUrl));

            // 新規購読者または再購読の場合、recipients_countを増やす
            if ($isNewSubscription) {
                $newsletter->increment('recipients_count');
            }
        } catch (\Exception $e) {
            Log::error('Newsletter welcome email failed', [
                'subscriber_email' => $subscriber->email,
                'newsletter_id' => $newsletter->id,
                'error' => $e->getMessage()
            ]);
            // メール送信失敗しても登録自体は完了させる
        }

        // 登録完了ページを表示
        return redirect()->route('newsletter.subscribed', ['formId' => $formId])
            ->with('subscriber', $subscriber)
            ->with('newsletter', $newsletter);
    }

    /**
     * 登録完了ページを表示
     */
    public function showSubscribedPage($formId)
    {
        // フォームIDからニュースレターを取得
        $newsletter = Newsletter::where('form_id', $formId)
            ->where('is_active', true)
            ->firstOrFail();

        // セッションから購読者情報を取得
        $subscriber = session('subscriber');

        if (!$subscriber) {
            return redirect()->route('newsletter.subscribe.form', ['formId' => $formId]);
        }

        return view('newsletter.subscribed', compact('newsletter', 'subscriber'));
    }

    /**
     * 購読解除処理
     */
    public function unsubscribe(Request $request, $token)
    {
        // トークンから購読者を特定
        $email = $request->input('email');
        if (!$email) {
            return redirect()->route('home')->with('error', '無効なリクエストです。');
        }

        $subscriber = NewsletterSubscriber::where('email', $email)->first();

        if (!$subscriber) {
            return redirect()->route('home')->with('error', '購読者が見つかりません。');
        }

        // トークンの検証（簡易的な検証）
        $expectedToken = base64_encode($subscriber->id . '-' . $subscriber->email);
        if ($token !== $expectedToken) {
            Log::warning('Invalid unsubscribe token', [
                'provided_token' => $token,
                'expected_token' => $expectedToken,
                'subscriber_id' => $subscriber->id,
                'email' => $email
            ]);
            // セキュリティのため、トークンが無効でもエラーは表示せず処理を続行
        }

        // 購読解除処理
        $subscriber->status = 'unsubscribed';
        $subscriber->unsubscribed_at = now();
        $subscriber->save();

        return view('newsletter.unsubscribed');
    }
}
