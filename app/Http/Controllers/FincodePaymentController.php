<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Models\Course;
use App\Models\Student;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;

class FincodePaymentController extends Controller
{
	// Fincode APIのベースURL
	private $apiBaseUrl;

	// Fincode APIのヘッダー
	private $apiHeaders;

	/**
	 * コンストラクタ
	 */
	public function __construct()
	{
		// 環境に応じてAPIのURLを設定
		$environment = env('FINCODE_ENVIRONMENT', 'test');
		$this->apiBaseUrl = $environment === 'production'
			? 'https://api.fincode.jp'
			: 'https://api.test.fincode.jp';

		// APIヘッダーを設定 - API_SECRETを使用
		$this->apiHeaders = [
			'Content-Type' => 'application/json',
			'Authorization' => 'Bearer ' . env('FINCODE_API_SECRET'),
		];
	}

	/**
	 * 購入者情報入力ページを表示
	 */
	public function showCustomerInfoForm(Request $request)
	{
		// リクエストからコースIDを取得
		$courseId = $request->input('course_id');

		if (!$courseId) {
			return redirect()->route('landing')->with('error', 'コースIDが指定されていません');
		}

		// コース情報を取得
		$course = Course::find($courseId);

		if (!$course) {
			return redirect()->route('landing')->with('error', '指定されたコースが見つかりません');
		}

		return view('payment.customer_info', compact('course'));
	}

	/**
	 * 購入者情報を保存し、決済ページにリダイレクト
	 */
	public function storeCustomerInfo(Request $request)
	{
		// バリデーション
		$validator = Validator::make($request->all(), [
			'name' => 'required|string|max:255',
			'email' => 'required|email|max:255',
			'phone' => 'nullable|string|max:20',
			'course_id' => 'required|exists:courses,id',
			'terms' => 'required',
		], [
			'name.required' => 'お名前は必須です',
			'email.required' => 'メールアドレスは必須です',
			'email.email' => '有効なメールアドレスを入力してください',
			'course_id.required' => 'コースIDが指定されていません',
			'course_id.exists' => '指定されたコースが見つかりません',
			'terms.required' => '利用規約とプライバシーポリシーへの同意が必要です',
		]);

		if ($validator->fails()) {
			return redirect()->back()->withErrors($validator)->withInput();
		}

		// コース情報を取得
		$course = Course::find($request->course_id);

		// 注文IDを生成
		$orderId = uniqid('order_');

		// 購入者情報をデータベースに保存
		try {
			$student = Student::create([
				'name' => $request->name,
				'email' => $request->email,
				'phone' => $request->phone,
				'order_id' => $orderId,
				'course_id' => $request->course_id,
				'payment_status' => 'pending',
				'payment_type' => 'Card', // デフォルトはカード決済
				'amount' => $course->price,
			]);

			// 決済ページへリダイレクト
			return $this->redirectToPayment($student);

		} catch (\Exception $e) {
			Log::error('購入者情報保存エラー', [
				'message' => $e->getMessage(),
				'trace' => $e->getTraceAsString(),
			]);
			return redirect()->back()->with('error', '購入者情報の保存中にエラーが発生しました。もう一度お試しください。')->withInput();
		}
	}

	/**
	 * Fincode決済ページにリダイレクト
	 *
	 * @param Student $student 購入者情報
	 * @param string $payType 決済タイプ（デフォルト: Card）
	 * @return \Illuminate\Http\RedirectResponse
	 */
	private function redirectToPayment(Student $student, $payType = null)
	{
		// 決済URL取得APIのエンドポイント
		$endpoint = '/v1/sessions';

		// 決済タイプが指定されていない場合は、studentのpayment_typeを使用
		$payType = $payType ?? $student->payment_type ?? 'Card';

		// 決済タイプをstudentに保存
		if ($student->payment_type !== $payType) {
			$student->payment_type = $payType;
			$student->save();
		}

		// 決済URL取得APIのリクエストデータ
		$data = [
			'success_url' => url('/api/payment/success') . '?code=success&id=' . urlencode($student->order_id),
			'cancel_url' => url('/api/payment/cancel') . '?code=dismiss&id=' . urlencode($student->order_id),
			'pay_type' => $payType,
			'method_type' => 'GET',
			'transaction' => [
				'amount' => (string) $student->amount, // 文字列として送信
				'order_id' => $student->order_id,
				'client_field_1' => $student->course->name,
				'client_field_2' => 'course_name:' . $student->course->name,
				'client_field_3' => 'student_id:' . $student->id,
			],
			'card' => [
				'job_code' => 'CAPTURE', // 即時売上（CAPTURE）に設定
				'tds_type' => '0' // 3Dセキュアを利用しない設定
			]
		];

		try {
			// Fincode APIを呼び出し
			$response = Http::withHeaders($this->apiHeaders)
				->post($this->apiBaseUrl . $endpoint, $data);

			// レスポンスをログに記録
			Log::info('Fincode 決済URL作成 レスポンス', [
				'response' => $response->json(),
				'status_code' => $response->status(),
				'request_data' => $data,
				'api_url' => $this->apiBaseUrl . $endpoint,
			]);

			// レスポンスが成功しなかった場合
			if (!$response->successful()) {
				Log::error('Fincode 決済URL作成 エラー', [
					'error' => $response->body(),
				]);
				return redirect()->route('payment.customer_info', ['course_id' => $student->course_id])
					->with('error', '決済URLの作成に失敗しました。もう一度お試しください。');
			}

			// レスポンスから決済URLを取得
			$responseData = $response->json();
			$paymentUrl = $responseData['link_url'] ?? null;

			if (!$paymentUrl) {
				Log::error('Fincode 決済URL取得 エラー', [
					'response' => $responseData,
				]);
				return redirect()->route('payment.customer_info', ['course_id' => $student->course_id])
					->with('error', '決済URLの取得に失敗しました。もう一度お試しください。');
			}

			// 決済ページに直接リダイレクト
			return redirect()->away($paymentUrl);

		} catch (\Exception $e) {
			Log::error('Fincode API エラー', [
				'message' => $e->getMessage(),
				'trace' => $e->getTraceAsString(),
			]);
			return redirect()->route('payment.customer_info', ['course_id' => $student->course_id])
				->with('error', '決済処理の準備中にエラーが発生しました。もう一度お試しください。');
		}
	}

	/**
	 * 支払い成功ページ
	 */
	public function success(Request $request)
	{
		// API経由のリクエストかどうかを判定
		$isApiRequest = $request->is('api/*');

		// リクエスト内容をログに記録
		Log::info('Fincode 決済成功リクエスト', [
			'request' => $request->all(),
			'method' => $request->method(),
			'url' => $request->fullUrl(),
			'isApiRequest' => $isApiRequest,
		]);

		// 注文IDを取得
		$orderId = $request->input('id') ?? $request->query('id');
		$code = $request->input('code') ?? $request->query('code');


		// リクエストデータをログに記録
		Log::info('Fincode 決済成功パラメータ', [
			'orderId' => $orderId,
			'code' => $code,
			'method' => $request->method(),
		]);

		if (!$orderId || $code !== 'success') {
			if ($isApiRequest) {
				return response()->json([
					'success' => false,
					'message' => '決済情報が不足しています',
				], 400);
			} else {
				return redirect()->route('landing')->with('error', '決済情報が不足しています');
			}
		}

		// 受講者情報を取得
		$student = Student::where('order_id', $orderId)->first();

		if (!$student) {
			Log::error('Student not found for order ID: ' . $orderId);
			if ($isApiRequest) {
				return response()->json([
					'success' => false,
					'message' => '購入者情報が見つかりませんでした',
				], 404);
			} else {
				return redirect()->route('landing')->with('error', '購入者情報が見つかりませんでした');
			}
		}

		try {
			// 決済情報を直接order_idで取得するエンドポイント
			// 決済タイプを取得（デフォルトはCard）
			$payType = $student->payment_type ?? 'Card';
			$endpoint = '/v1/payments/' . urlencode($orderId) . '?pay_type=' . $payType;
			$response = Http::withHeaders($this->apiHeaders)
				->get($this->apiBaseUrl . $endpoint);

			// レスポンスをログに記録
			Log::info('Fincode 決済情報取得リクエスト', [
				'endpoint' => $endpoint,
				'order_id' => $orderId,
			]);

			Log::info('Fincode 決済情報取得レスポンス', [
				'responseData' => $response->json(),
			]);

			if (!$response->successful()) {
				Log::error('Fincode payment retrieval failed: ' . $response->body());
				if ($isApiRequest) {
					return response()->json([
						'success' => false,
						'message' => '決済情報の取得に失敗しました',
					], 500);
				} else {
					return redirect()->route('landing')->with('error', '決済情報の取得に失敗しました');
				}
			}

			$responseData = $response->json();

			// レスポンスデータをログに記録
			Log::info('Fincode 決済情報取得レスポンス part2', [
				'responseData' => $responseData,
			]);

			// レスポンスデータから決済情報を取得
			$paymentInfo = null;
			$sessionInfo = null;

			// レスポンスデータの構造を確認
			if (!empty($responseData['list'])) {
				// 決済一覧が返ってきた場合、最初の決済情報を使用
				$paymentInfo = $responseData['list'][0];
				Log::info('Fincode 決済情報をリストから取得しました', ['payment_info' => $paymentInfo]);
			} elseif (!empty($responseData['payments'])) {
				// payments配列がある場合
				$paymentInfo = $responseData['payments'][0];
				Log::info('Fincode payments配列から決済情報を取得しました', ['payment_info' => $paymentInfo]);
			} elseif (!empty($responseData['id'])) {
				// 直接レスポンスが返ってくる場合
				$paymentInfo = $responseData;
				Log::info('Fincode 直接レスポンスから決済情報を取得しました', ['payment_info' => $paymentInfo]);
			}


			// 決済が完了しているか確認
			// 注意: ステータスの値はレスポンスによって異なる可能性がある
			$status = $paymentInfo['status'] ?? '';
			if ($status !== 'AUTHORIZED' && $status !== 'CAPTURED' && $status !== 'CREATE') {
				Log::error('Fincode payment status is not completed: ' . $status);
				if ($isApiRequest) {
					return response()->json([
						'success' => false,
						'message' => '決済が完了していません',
						'status' => $status,
					], 400);
				} else {
					return redirect()->route('landing')->with('error', '決済が完了していません');
				}
			}

			// 受講者情報を更新
			$student->payment_status = 'completed';

			// payment_idがダミーでない場合のみ更新
			if (!empty($paymentInfo['id']) && strpos($paymentInfo['id'], 'dummy_') !== 0) {
				$student->payment_id = $paymentInfo['id'];
				Log::info('Real payment ID saved: ' . $paymentInfo['id']);
			} elseif (!empty($paymentInfo['id']) && strpos($paymentInfo['id'], 'session_') === 0) {
				// セッションIDから生成された決済IDの場合
				$student->payment_id = $paymentInfo['id'];
				Log::info('Session-based payment ID saved: ' . $paymentInfo['id']);
			} elseif (empty($student->payment_id)) {
				// payment_idが空の場合のみダミーを設定
				$student->payment_id = $paymentInfo['id'];
				Log::warning('Dummy payment ID saved: ' . $paymentInfo['id']);
			}

			// payment_responseフィールドに決済情報をJSON形式で保存
			// 配列をJSON文字列に変換してから保存する
			$responseInfo = [
				'payment_info' => $paymentInfo,
				'response_data' => $responseData,
				'timestamp' => now()->toDateTimeString(),
			];

			// セッション情報があれば追加
			if (isset($sessionInfo)) {
				$responseInfo['session_info'] = $sessionInfo;
			}

			$student->payment_response = json_encode($responseInfo);

			// 変更を保存
			$student->save();

			// 保存結果をログに記録
			Log::info('Student payment info updated', [
				'student_id' => $student->id,
				'order_id' => $student->order_id,
				'payment_id' => $student->payment_id,
				'payment_status' => $student->payment_status,
			]);

			// ユーザーが認証済みの場合、コースへのアクセス権を付与
			if (auth()->check()) {
				$user = auth()->user();

				// ユーザーがまだこのコースを持っていない場合のみ追加
				if (!$user->courses()->where('course_id', $student->course_id)->exists()) {
					$user->courses()->attach($student->course_id);
				}
			}

			// 成功時のレスポンス
			if ($isApiRequest) {
				// APIリクエストの場合はリダイレクト用のページを表示
				// JavaScriptが自動的にリダイレクトを処理する
				$responseData = [
					'success' => true,
					'message' => '決済が完了しました',
					'order_id' => $student->order_id,
					'payment_id' => $student->payment_id,
					'redirect_url' => route('payment.success') . '?code=success&id=' . urlencode($student->order_id),
				];

				// レスポンスデータをログに記録
				Log::info('Fincode 決済成功レスポンス', [
					'responseData' => $responseData,
				]);

				// リダイレクト用のページを返す
				// JavaScriptでリダイレクトを処理するためのページを返す
				return view('payment.api-response', ['data' => $responseData]);
			} else {
				// 購入完了ページを表示
				return view('payment.complete', compact('student'));
			}

		} catch (\Exception $e) {
			Log::error('Fincode API error: ' . $e->getMessage());
			if ($isApiRequest) {
				$responseData = [
					'success' => false,
					'message' => '決済情報の取得中にエラーが発生しました',
					'error' => $e->getMessage(),
					'redirect_url' => route('landing'),
				];

				// レスポンスデータをログに記録
				Log::error('Fincode 決済エラーレスポンス', [
					'responseData' => $responseData,
					'error' => $e->getMessage(),
				]);

				// JavaScriptでリダイレクトを処理するためのページを返す
				return view('payment.api-response', ['data' => $responseData]);
			} else {
				return redirect()->route('landing')->with('error', '決済情報の取得中にエラーが発生しました');
			}
		}
	}

	/**
	 * 支払いキャンセルページ
	 */
	public function cancel(Request $request)
	{
		// API経由のリクエストかどうかを判定
		$isApiRequest = $request->is('api/*');

		// キャンセル情報をログに記録
		Log::info('Fincode 決済キャンセル', [
			'request' => $request->all(),
			'method' => $request->method(),
			'url' => $request->fullUrl(),
			'isApiRequest' => $isApiRequest,
		]);

		$code = $request->input('code') ?? $request->query('code');
		$orderId = $request->input('id') ?? $request->query('id');

		if ($code !== 'dismiss') {
			Log::warning('Fincode 決済キャンセル: 不明なコード', ['code' => $code]);
		}

		// 受講者情報があればステータスを更新
		if ($orderId) {
			$student = Student::where('order_id', $orderId)->first();
			if ($student) {
				$student->payment_status = 'cancelled';
				$student->save();
			}
		}

		// レスポンスを返す
		if ($isApiRequest) {
			return response()->json([
				'success' => true,
				'message' => '決済がキャンセルされました',
				'order_id' => $orderId,
				'redirect_url' => route('payment.cancel') . '?code=dismiss&id=' . urlencode($orderId),
			], 200);
		} else {
			// キャンセルページを表示
			return view('payment.cancel');
		}
	}
}
