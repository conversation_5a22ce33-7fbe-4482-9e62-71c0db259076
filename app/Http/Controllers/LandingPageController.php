<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Course;
use App\Models\Faq;

class LandingPageController extends Controller
{
    /**
     * Display the landing page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        // Get the main course (assuming there's a default course with ID 1)
        // If Course model doesn't exist yet, we'll use a placeholder
       $course = Course::find(1);

        // Get FAQs if the model exists
        $faqs = [];
        if (class_exists('App\Models\Faq')) {
            $faqs = Faq::all();
        }

        // Get content for modals
        $privacyPolicyContent = $this->getPrivacyPolicyContent();
        $termsOfServiceContent = $this->getTermsOfServiceContent();

        return view('lands.land', [
            'course' => $course,
            'faqs' => $faqs,
            'privacyPolicyContent' => $privacyPolicyContent,
            'termsOfServiceContent' => $termsOfServiceContent,
        ]);
    }

    /**
     * Get the privacy policy content.
     *
     * @return string
     */
    private function getPrivacyPolicyContent()
    {
        // In a real application, this might come from a database or CMS
        return '<h4>プライバシーポリシー</h4>
        <p>当サイトは、お客様の個人情報を適切に管理し、保護することを約束します。</p>
        <h5>1. 収集する情報</h5>
        <p>当サイトでは、以下の情報を収集することがあります：</p>
        <ul>
            <li>氏名</li>
            <li>メールアドレス</li>
            <li>住所</li>
            <li>電話番号</li>
            <li>支払い情報</li>
        </ul>
        <h5>2. 情報の利用目的</h5>
        <p>収集した情報は以下の目的で利用します：</p>
        <ul>
            <li>サービス提供のため</li>
            <li>お問い合わせへの対応</li>
            <li>サービス改善のための分析</li>
            <li>新サービスのご案内（お客様の同意がある場合）</li>
        </ul>
        <h5>3. 第三者への提供</h5>
        <p>当サイトは、法律で定められた場合を除き、お客様の同意なく個人情報を第三者に提供することはありません。</p>
        <h5>4. セキュリティ</h5>
        <p>当サイトは、お客様の個人情報を適切に保護するためのセキュリティ対策を講じています。</p>
        <h5>5. Cookieの使用</h5>
        <p>当サイトでは、サービス向上のためにCookieを使用しています。ブラウザの設定でCookieを無効にすることも可能です。</p>
        <h5>6. お問い合わせ</h5>
        <p>プライバシーポリシーに関するお問い合わせは、当サイトのお問い合わせフォームからお願いします。</p>';
    }

    /**
     * Get the terms of service content.
     *
     * @return string
     */
    private function getTermsOfServiceContent()
    {
        // In a real application, this might come from a database or CMS
        return '<h4>利用規約</h4>
        <p>本規約は、当サイトが提供するサービスの利用条件を定めるものです。</p>
        <h5>1. サービス内容</h5>
        <p>当サイトは、タロット講座および関連するコンテンツを提供します。</p>
        <h5>2. 会員登録</h5>
        <p>サービスの利用には会員登録が必要です。登録情報は正確かつ最新の情報を提供してください。</p>
        <h5>3. 禁止事項</h5>
        <p>以下の行為を禁止します：</p>
        <ul>
            <li>法令違反行為</li>
            <li>他のユーザーへの迷惑行為</li>
            <li>サービスの運営を妨げる行為</li>
            <li>コンテンツの無断複製・転載</li>
        </ul>
        <h5>4. 知的財産権</h5>
        <p>当サイトのコンテンツに関する知的財産権は、当サイトまたはライセンサーに帰属します。</p>
        <h5>5. 免責事項</h5>
        <p>当サイトは、サービスの完全性、正確性、有用性等を保証するものではありません。</p>
        <h5>6. 規約の変更</h5>
        <p>当サイトは、必要に応じて本規約を変更することがあります。変更後の規約は、当サイトに掲載した時点で効力を生じます。</p>
        <h5>7. 準拠法と管轄裁判所</h5>
        <p>本規約の解釈および適用は日本法に準拠し、紛争が生じた場合は東京地方裁判所を第一審の専属的合意管轄裁判所とします。</p>';
    }
}
