<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminMiddleware
{
    /**
     * 特定のメールアドレスを持つユーザーのみアクセスを許可するミドルウェア
     * 条件を満たさない場合は404エラーを表示
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // ユーザーがログインしていない場合は404エラーを表示
        if (!Auth::check()) {
            abort(404);
        }

        // ユーザーのメールアドレスが管理者のものでない場合は404エラーを表示
        if (Auth::user()->email !== '<EMAIL>') {
            abort(404);
        }

        return $next($request);
    }
}
