/**
 * Fincode決済リダイレクト処理
 * 
 * このスクリプトは、Fincode決済後のリダイレクト処理を行います。
 * APIからのJSONレスポンスを受け取り、完了ページにリダイレクトします。
 */
document.addEventListener('DOMContentLoaded', function() {
    // JSONレスポンスを取得
    const jsonContent = document.body.textContent || document.body.innerText;
    
    if (jsonContent && jsonContent.trim()) {
        try {
            // JSONをパース
            const responseData = JSON.parse(jsonContent);
            
            // 成功レスポンスの場合、リダイレクトURLがあればリダイレクト
            if (responseData.success && responseData.redirect_url) {
                console.log('Payment successful, redirecting to:', responseData.redirect_url);
                window.location.href = responseData.redirect_url;
            } else if (!responseData.success) {
                console.error('Payment error:', responseData.message);
                alert('決済処理中にエラーが発生しました: ' + responseData.message);
                window.location.href = '/';
            }
        } catch (e) {
            // JSONパースエラーの場合は何もしない（通常のHTMLページの場合）
            console.log('Not a JSON response, normal page rendering');
        }
    }
});
