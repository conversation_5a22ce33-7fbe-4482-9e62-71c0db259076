:root {
		--primary-color: #9c6bae;
		--primary-light: #d7b8e0;
		--primary-dark: #7a4989;
		--secondary-color: #f5eef7;
		--accent-color: #ffd6e0;
		--text-color: #333;
		--text-light: #666;
		--white: #fff;
		--black: #000;
		--gray-light: #f8f8f8;
		--gray: #e0e0e0;
		--gray-dark: #888;
		--shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
		--radius: 8px;
		--radius-lg: 16px;
		--spacing-xs: 0.25rem;
		--spacing-sm: 0.5rem;
		--spacing-md: 1rem;
		--spacing-lg: 2rem;
		--spacing-xl: 3rem;
		--font-primary: 'M PLUS 1p', 'Noto Sans JP', sans-serif;
		--font-display: 'M PLUS 1p', sans-serif;
		--transition: all 0.3s ease;
	}

	* {
		margin: 0;
		padding: 0;
		box-sizing: border-box;
	}

	body {
		font-family: var(--font-primary);
		color: var(--text-color);
		line-height: 1.6;
		background-color: var(--white);
		font-weight: 300;
	}

	.container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 var(--spacing-lg);
	}

	h1, h2, h3, h4, h5, h6 {
		line-height: 1.3;
		margin-bottom: var(--spacing-md);
		font-weight: 700;
	}

	p {
		margin-bottom: var(--spacing-md);
	}

	a {
		color: var(--primary-color);
		text-decoration: none;
		transition: var(--transition);
	}

	a:hover {
		color: var(--primary-dark);
	}

	img {
		max-width: 100%;
		height: auto;
		border-radius: var(--radius);
	}

	.section-title {
		font-family: var(--font-display);
		font-size: 2.5rem;
		color: var(--primary-dark);
		text-align: center;
		margin-bottom: var(--spacing-xl);
		position: relative;
		font-weight: 700;
	}

	.section-title::after {
		content: "";
		position: absolute;
		bottom: -10px;
		left: 50%;
		transform: translateX(-50%);
		width: 80px;
		height: 3px;
		background-color: var(--accent-color);
	}

	.btn {
		display: inline-block;
		padding: 0.8rem 2rem;
		border-radius: 50px;
		font-weight: 500;
		text-align: center;
		cursor: pointer;
		transition: var(--transition);
		text-decoration: none;
	}

	.btn-primary {
		background-color: var(--primary-color);
		color: var(--white);
		box-shadow: var(--shadow);
	}

	.btn-primary:hover {
		background-color: var(--primary-dark);
		color: var(--white);
		transform: translateY(-3px);
	}

	/* Header Styles */
	header {
		background-color: var(--white);
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
		padding: var(--spacing-md) 0;
		position: sticky;
		top: 0;
		z-index: 1000;
	}

	header .container {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.logo h1 {
		font-family: 'M PLUS 1p', var(--font-display);
		font-size: 1.5rem;
		color: var(--primary-color);
		margin: 0;
		font-weight: 500;
	}

	nav ul {
		display: flex;
		list-style: none;
	}

	nav ul li {
		margin-left: var(--spacing-lg);
	}

	nav ul li a {
		color: var(--text-color);
		font-weight: 500;
	}

	nav ul li a:hover {
		color: var(--primary-color);
	}

	/* Hero Section */
	.hero {
		padding: var(--spacing-xl) 0;
		background: linear-gradient(135deg, var(--secondary-color) 0%, var(--white) 100%);
	}

	.hero .container {
		display: flex;
		align-items: center;
		gap: var(--spacing-xl);
	}

	.hero-content {
		flex: 1;
	}

	.hero-content h1 {
		font-family: var(--font-display);
		font-size: 2.8rem;
		color: var(--primary-dark);
		margin-bottom: var(--spacing-sm);
		font-weight: 700;
	}

	.hero-content .subtitle {
		font-size: 1.4rem;
		color: var(--primary-color);
		font-weight: 500;
		margin-bottom: var(--spacing-lg);
	}

	.hero-text p {
		margin-bottom: var(--spacing-md);
		font-size: 1.1rem;
	}

	.hero-image {
		flex: 1;
		text-align: center;
	}

	.hero-image img {
		border-radius: var(--radius-lg);
		box-shadow: var(--shadow);
		max-width: 90%;
	}

	/* Features Section */
	#features {
		padding: var(--spacing-xl) 0;
		background-image: url('img/tarotcards.jpg');
		background-size: cover;
		background-position: center;
		background-attachment: fixed;
		position: relative;
		z-index: 0;
	}

	#features::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 255, 0.2);
		z-index: 1;
	}

	#features .container {
		position: relative;
		z-index: 2;
	}

	.features-grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
		gap: var(--spacing-lg);
	}

	.feature-card {
		background-color: rgba(245, 238, 247, 0.95);
		padding: var(--spacing-lg);
		border-radius: var(--radius);
		text-align: center;
		transition: var(--transition);
		backdrop-filter: blur(5px);
	}

	.feature-card:hover {
		transform: translateY(-5px);
		box-shadow: var(--shadow);
	}

	.feature-icon {
		margin-bottom: var(--spacing-md);
		font-size: 2.5rem;
		color: var(--primary-color);
	}

	.feature-card h3 {
		font-size: 1.3rem;
		margin-bottom: var(--spacing-sm);
		color: var(--primary-dark);
	}

	/* Techniques Section */
	.techniques {
		padding: var(--spacing-xl) 0;
		background-color: var(--gray-light);
	}

	.techniques-container {
		display: flex;
		align-items: center;
		gap: var(--spacing-xl);
	}

	.technique-image {
		flex: 1;
	}

	.technique-content {
		flex: 1;
	}

	.technique-item {
		display: flex;
		align-items: center;
		margin-bottom: var(--spacing-md);
	}

	.technique-item i {
		color: var(--primary-color);
		font-size: 1.5rem;
		margin-right: var(--spacing-md);
	}

	.technique-item p {
		font-size: 1.1rem;
		margin: 0;
	}

	.quote {
		background-color: var(--primary-light);
		padding: var(--spacing-lg);
		border-radius: var(--radius);
		margin-top: var(--spacing-lg);
		font-style: italic;
		color: var(--primary-dark);
	}

	.quote p {
		margin: 0;
		font-size: 1.2rem;
		line-height: 1.5;
	}

	/* Instructor Section */
	.instructor {
		padding: var(--spacing-xl) 0;
		background-color: var(--white);
	}

	.instructor-profile {
		display: flex;
		gap: var(--spacing-xl);
		align-items: center;
		max-width: 900px;
		margin: 0 auto;
	}

	.instructor-image {
		flex: 1;
		text-align: center;
	}

	.instructor-image img {
		border-radius: 50%;
		box-shadow: var(--shadow);
		max-width: 80%;
	}

	.instructor-info {
		flex: 2;
	}

	.instructor-info h3 {
		font-size: 1.8rem;
		color: var(--primary-dark);
		margin-bottom: var(--spacing-xs);
	}

	.instructor-title {
		font-size: 1.2rem;
		color: var(--primary-color);
		margin-bottom: var(--spacing-md);
	}

	.instructor-achievements {
		margin-bottom: var(--spacing-lg);
	}

	.achievement {
		display: flex;
		align-items: center;
		margin-bottom: var(--spacing-sm);
	}

	.achievement i {
		color: var(--primary-color);
		margin-right: var(--spacing-md);
	}

	.instructor-message {
		font-style: italic;
		background-color: var(--secondary-color);
		padding: var(--spacing-md);
		border-radius: var(--radius);
		border-left: 4px solid var(--primary-color);
	}

	/* Contents Section */
	#contents {
		padding: var(--spacing-xl) 0;
		background-image: url('https://images.unsplash.com/photo-1600430188203-bbb8dac79646?q=80&w=1887&auto=format&fit=crop');
		background-size: cover;
		background-position: center;
		background-attachment: fixed;
		position: relative;
		z-index: 0;
	}

	#contents::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(255, 255, 255, 0.9);
		z-index: 1;
	}

	#contents .container {
		position: relative;
		z-index: 2;
	}

	.contents-grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
		gap: var(--spacing-lg);
	}

	.content-card {
		background-color: rgba(245, 238, 247, 0.95);
		padding: var(--spacing-lg);
		border-radius: var(--radius);
		display: flex;
		align-items: flex-start;
		transition: var(--transition);
		backdrop-filter: blur(5px);
	}

	.content-card:hover {
		transform: translateY(-5px);
		box-shadow: var(--shadow);
	}

	.content-icon {
		font-size: 2rem;
		color: var(--primary-color);
		margin-right: var(--spacing-md);
	}

	.content-info h3 {
		font-size: 1.2rem;
		margin-bottom: var(--spacing-xs);
		color: var(--primary-dark);
	}

	.content-info p {
		margin: 0;
		font-size: 0.95rem;
	}

	.contents-image {
		text-align: center;
		margin-bottom: var(--spacing-xl);
	}

	.contents-image img {
		max-width: 80%;
		border-radius: var(--radius-lg);
		box-shadow: var(--shadow);
	}

	/* Session Section */
	.session {
		padding: var(--spacing-xl) 0;
		background-color: var(--white);
	}

	.session-intro {
		max-width: 800px;
		margin: 0 auto var(--spacing-xl);
		text-align: center;
		font-size: 1.1rem;
	}

	.session-details {
		background-color: var(--secondary-color);
		border-radius: var(--radius-lg);
		padding: var(--spacing-xl);
		max-width: 900px;
		margin: 0 auto;
	}

	.session-info {
		display: flex;
		justify-content: center;
		gap: var(--spacing-xl);
		margin-bottom: var(--spacing-lg);
	}

	.session-price, .session-duration {
		text-align: center;
		background-color: var(--white);
		padding: var(--spacing-md) var(--spacing-lg);
		border-radius: var(--radius);
		box-shadow: var(--shadow);
	}

	.price-label, .duration-label {
		display: block;
		font-size: 0.9rem;
		color: var(--text-light);
		margin-bottom: var(--spacing-xs);
	}

	.price-value, .duration-value {
		font-size: 1rem;
		font-weight: 700;
		color: var(--primary-dark);
	}

	.features-title {
		text-align: center;
		margin: var(--spacing-lg) 0;
		font-size: 1.5rem;
		color: var(--primary-dark);
	}

	.session-features {
		margin-bottom: var(--spacing-lg);
	}

	.session-feature {
		background-color: var(--white);
		padding: var(--spacing-lg);
		border-radius: var(--radius);
		margin-bottom: var(--spacing-md);
	}

	.session-feature h4 {
		color: var(--primary-color);
		margin-bottom: var(--spacing-sm);
	}

	.session-closing {
		text-align: center;
		font-style: italic;
		margin-top: var(--spacing-lg);
	}

	.session-image {
		text-align: center;
		margin-bottom: var(--spacing-xl);
	}

	.session-image img {
		max-width: 80%;
		border-radius: var(--radius-lg);
		box-shadow: var(--shadow);
	}

	/* Recommendations Section */
	.recommendations {
		padding: var(--spacing-xl) 0;
		background-image: url('img/magic.jpg');
		background-size: cover;
		background-position: center;
		background-attachment: fixed;
		position: relative;
		z-index: 0;
	}

	.recommendations::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(255, 255, 255, 0.9);
		z-index: 1;
	}

	.recommendations .container {
		position: relative;
		z-index: 2;
	}

	.recommendations-list {
		display: flex;
		flex-direction: column;
		max-width: 800px;
		margin: 0 auto;
		gap: var(--spacing-md);
	}

	.recommendation-item {
		display: flex;
		align-items: center;
		background-color: rgba(245, 238, 247, 0.95);
		padding: var(--spacing-lg);
		border-radius: var(--radius);
		box-shadow: var(--shadow);
		backdrop-filter: blur(5px);
	}

	.recommendation-icon {
		font-size: 1.8rem;
		color: var(--primary-color);
		margin-right: var(--spacing-lg);
	}

	.recommendation-item p {
		margin: 0;
		font-size: 1.1rem;
	}

	/* Steps Section */
	.steps {
		padding: var(--spacing-xl) 0;
		background-color: var(--gray-light);
	}

	.steps-timeline {
		max-width: 800px;
		margin: 0 auto;
		position: relative;
	}

	.steps-timeline::before {
		content: '';
		position: absolute;
		left: 40px;
		top: 0;
		bottom: 0;
		width: 2px;
		background-color: var(--primary-light);
	}

	.step {
		display: flex;
		margin-bottom: var(--spacing-lg);
		position: relative;
	}

	.step-number {
		width: 80px;
		height: 80px;
		background-color: var(--primary-color);
		color: var(--white);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 1.8rem;
		font-weight: 700;
		z-index: 1;
		box-shadow: var(--shadow);
	}

	.step-content {
		flex: 1;
		background-color: var(--white);
		padding: var(--spacing-lg);
		border-radius: var(--radius);
		margin-left: var(--spacing-md);
		box-shadow: var(--shadow);
	}

	.step-content h3 {
		color: var(--primary-dark);
		margin-bottom: var(--spacing-xs);
	}

	.step-content p {
		margin: 0;
	}

	.steps-image {
		text-align: center;
		margin-bottom: var(--spacing-xl);
	}

	.steps-image img {
		max-width: 80%;
		border-radius: var(--radius-lg);
		box-shadow: var(--shadow);
	}

	/* FAQ Section */
	.faq {
		padding: var(--spacing-xl) 0;
		background-color: var(--white);
	}

	.faq-container {
		max-width: 800px;
		margin: 0 auto;
	}

	.faq-item {
		margin-bottom: var(--spacing-md);
		border-radius: var(--radius);
		overflow: hidden;
		box-shadow: var(--shadow);
	}

	.faq-question {
		background-color: var(--secondary-color);
		padding: var(--spacing-lg);
		cursor: pointer;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.faq-question h3 {
		margin: 0;
		font-size: 1.2rem;
		color: var(--primary-dark);
	}

	.faq-toggle {
		color: var(--primary-color);
		font-size: 1.2rem;
	}

	.faq-answer {
		background-color: var(--white);
		padding: 0 var(--spacing-lg);
		max-height: 0;
		overflow: hidden;
		transition: max-height 0.3s ease;
	}

	.faq-answer p {
		padding: var(--spacing-md) 0;
	}

	/* Price Section */
	#price {
		padding: var(--spacing-xl) 0;
		background-image: url('img/cosmo.jpg');
		background-size: cover;
		background-position: center;
		background-attachment: fixed;
		position: relative;
		z-index: 0;
	}

	#price::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(255, 255, 255, 0.2);
		z-index: 1;
	}

	.price .container {
		position: relative;
		z-index: 2;
	}

	.price-card {
		background-color: rgba(245, 238, 247, 0.95);
		border-radius: var(--radius-lg);
		overflow: hidden;
		box-shadow: var(--shadow);
		backdrop-filter: blur(5px);
	}

	.price-header {
		background-color: var(--primary-color);
		padding: var(--spacing-lg);
		text-align: center;
	}

	.price-header h3 {
		color: var(--white);
		margin: 0;
		font-size: 1.3rem;
	}

	.price-amount {
		text-align: center;
		padding: var(--spacing-lg) 0;
	}

	.price-value {
		font-size: 3rem;
		font-weight: 700;
		color: var(--primary-dark);
	}

	.price-currency {
		font-size: 1.5rem;
		font-weight: 500;
		color: var(--primary-dark);
	}

	.price-tax {
		display: block;
		font-size: 0.9rem;
		color: var(--text-light);
		margin-top: var(--spacing-xs);
	}

	.price-includes {
		padding: 0 var(--spacing-lg) var(--spacing-lg);
	}

	.price-includes p {
		font-weight: 500;
		margin-bottom: var(--spacing-sm);
	}

	.price-includes ul {
		list-style-position: inside;
		color: var(--text-light);
	}

	.price-includes li {
		margin-bottom: var(--spacing-xs);
	}

	/* CTA Section */
	.cta-section {
		padding: var(--spacing-xl) 0;
		background: linear-gradient(135deg, var(--primary-light) 0%, var(--accent-color) 100%);
	}

	.cta-content {
		max-width: 800px;
		margin: 0 auto;
		text-align: center;
	}

	.cta-content h2 {
		font-size: 2rem;
		color: var(--primary-dark);
		margin-bottom: var(--spacing-md);
	}

	.cta-content p {
		font-size: 1.2rem;
		margin-bottom: var(--spacing-lg);
	}

	.cta-statistic {
		background-color: var(--white);
		padding: var(--spacing-md);
		border-radius: var(--radius);
		margin-bottom: var(--spacing-lg);
		display: inline-block;
	}

	.cta-statistic p {
		margin: 0;
		font-weight: 500;
		color: var(--primary-dark);
	}

	.cta-button .btn {
		padding: 1rem 3rem;
		font-size: 1.2rem;
	}

	/* Footer */
	footer {
		background-color: var(--primary-dark);
		color: var(--white);
		padding: var(--spacing-xl) 0 var(--spacing-md);
	}

	.footer-content {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: var(--spacing-xl);
		margin-bottom: var(--spacing-xl);
	}

	.footer-logo h2 {
		font-family: var(--font-display);
		font-size: 1.8rem;
		margin-bottom: var(--spacing-xs);
		font-weight: 700;
	}

	.footer-logo p {
		color: var(--gray-light);
	}

	.footer-links h3, .footer-contact h3 {
		font-size: 1.2rem;
		margin-bottom: var(--spacing-md);
	}

	.footer-links ul {
		list-style: none;
	}

	.footer-links li {
		margin-bottom: var(--spacing-sm);
	}

	.footer-links a {
		color: var(--gray-light);
	}

	.footer-links a:hover {
		color: var(--white);
	}

	.footer-contact p {
		color: var(--gray-light);
	}

	.contact-link {
		color: var(--accent-color);
	}

	.footer-copyright {
		text-align: center;
		padding-top: var(--spacing-lg);
		border-top: 1px solid rgba(255, 255, 255, 0.1);
		font-size: 0.9rem;
		color: var(--gray-light);
	}

	/* Responsive Design */
	@media (max-width: 992px) {
		.section-title {
			font-size: 2.2rem;
		}

		.hero .container {
			flex-direction: column;
		}

		.hero-content, .hero-image {
			flex: none;
			width: 100%;
		}

		.hero-content h1 {
			font-size: 2.4rem;
		}

		.techniques-container {
			flex-direction: column;
		}

		.technique-image, .technique-content {
			flex: none;
			width: 100%;
		}

		.instructor-profile {
			flex-direction: column;
			text-align: center;
		}

		.instructor-image, .instructor-info {
			flex: none;
			width: 100%;
		}

		.instructor-image {
			margin-bottom: var(--spacing-lg);
		}

		.achievement {
			justify-content: center;
		}
	}

	@media (max-width: 768px) {
		header .container {
			flex-direction: column;
		}

		.logo {
			margin-bottom: var(--spacing-md);
		}

		nav ul {
			justify-content: center;
		}

		nav ul li {
			margin: 0 var(--spacing-sm);
		}

		.section-title {
			font-size: 2rem;
		}

		.features-grid, .contents-grid {
			grid-template-columns: 1fr;
		}

		.session-info {
			flex-direction: column;
			align-items: center;
			gap: var(--spacing-md);
		}

		.session-price, .session-duration {
			width: 100%;
			max-width: 300px;
		}

		.step-number {
			width: 60px;
			height: 60px;
			font-size: 1.5rem;
		}

		.steps-timeline::before {
			left: 30px;
		}

		.price-value {
			font-size: 2.5rem;
		}
	}

	@media (max-width: 576px) {
		.container {
			padding: 0 var(--spacing-md);
		}

		.section-title {
			font-size: 1.8rem;
		}

		.hero-content h1 {
			font-size: 2rem;
		}

		.hero-content .subtitle {
			font-size: 1.2rem;
		}

		.feature-card, .content-card, .recommendation-item, .session-feature {
			padding: var(--spacing-md);
		}

		.session-details {
			padding: var(--spacing-lg);
		}

		.cta-content h2 {
			font-size: 1.8rem;
		}

		.cta-content p {
			font-size: 1.1rem;
		}

		.cta-button .btn {
			padding: 0.8rem 2rem;
			font-size: 1.1rem;
		}

		.footer-content {
			grid-template-columns: 1fr;
			gap: var(--spacing-lg);
			text-align: center;
		}
	}

	#price .section-title {
		color: var(--primary-dark);
		text-shadow:
				-1px -1px 0 var(--white),
				1px -1px 0 var(--white),
				-1px 1px 0 var(--white),
				1px 1px 0 var(--white);
		font-weight: 700;
	}

	#features .section-title {
		color: var(--primary-dark);
		text-shadow:
				-1px -1px 0 var(--white),
				1px -1px 0 var(--white),
				-1px 1px 0 var(--white),
				1px 1px 0 var(--white);
		font-weight: 700;
	}
