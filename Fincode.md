# リダイレクト型決済
	リダイレクト型決済
	1. 購入するボタンをクリック
	2. 決済URL取得APIを呼び出す
	3. 決済画面にリダイレクト
	4. 決済画面で決済を完了
	5. 決済結果取得APIを呼び出す
	6. 決済結果を表示

# ヘッダー情報
	1. ヘッダーは bearer . API_SECRET を送信
	2. $api_key = "Bearer ".FINCODE_API_SECRET;
	3. $endpoint = "https://api.test.fincode.jp/v1/sessions";
	4. curl_setopt($ch, CURLOPT_HTTPHEADER, array('Authorization: ' . $api_key));
	5. curl_setopt($ch, CURLOPT_HTTPHEADER, array('Authorization: ' . $api_key, 'Content-Type: application/json'));

	注意事項としては amount は Stringデータとして送付しなければエラーになる
	## 送付データ
	pay_typeパラメータはCard以外にも Paypay や konbini などもある

	```php
		$data = [
			"success_url" => "http://localhost/fincode/response.php?code=success&id=".urlencode($orderId),
			"cancel_url" => "http://localhost/fincode/response.php?code=dismiss",
			"pay_type" => "Card",

			"transaction" => [
				"amount" => (string) 13900,
				"order_id" => $orderId,
				'client_field_1' => "テスト商品"
			],
			'card' => [
				'job_code' => 'CAPTURE', // 即時売上（CAPTURE）に設定
				'tds_type' => '0' // 3Dセキュアを利用しない設定
			]
		];
	```
# 決済URL取得
	決済データの準備が出来たら、CURLで決済URL取得APIを呼び出す

	```php
	$ch = curl_init();
	curl_setopt($ch, CURLOPT_URL, $endpoint);
	curl_setopt($ch, CURLOPT_HTTPHEADER, [
		"Content-Type: application/json",
		"Authorization: " . $api_key,
	]);
	curl_setopt($ch, CURLOPT_POST, true);
	curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($ch, CURLOPT_VERBOSE, true);

	$response = curl_exec($ch);
	$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

	if ($http_code == 200) {
		$result = json_decode($response, true);
		$payment_url = $result['link_url'];
		header("Location: " . $payment_url);
		exit;
	} else {
		// エラー処理
		error_log("API Error: " . $response);
	}
	```

# 決済結果取得
　　決済の結果は送信データのSuccessとCancelの部分にオーダーIDがゲットで返される

	```php
	"success_url" => "http://localhost/fincode/response.php?code=success&id=".urlencode($orderId),
	```
	上記の場合はid= の部分にオーダーがIDが返るので $orderId = $_GET['id']; で取得できる
	それを元にAPIで問い合わせてレスポンスデータを取得

	```php
	$orderId = $_GET['id'];
	$api_key = "Bearer ".FINCODE_API_SECRET;
	$endpoint = "https://api.test.fincode.jp/v1/payments?order_id=".urlencode($orderId);
	$ch = curl_init();
	curl_setopt($ch, CURLOPT_URL, $endpoint);
	curl_setopt($ch, CURLOPT_HTTPHEADER, [
		"Content-Type: application/json",
		"Authorization: " . $api_key,
	]);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($ch, CURLOPT_VERBOSE, true);

	$response = curl_exec($ch);
	$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

	if ($http_code == 200) {
		$result = json_decode($response, true);
		// $payment_url = $result['link_url'];
		// header("Location: " . $payment_url);
		// exit;
	} else {
		// エラー処理
		error_log("API Error: " . $response);
	}
	```

# 決済一覧
決済情報の一覧は以下のようにする
参考: /Applications/XAMPP/xamppfiles/htdocs/fincode/orders.php

```php
// APIパラメータの設定
$params = [
    'pay_type' => 'Card', // 必須パラメータ: カード決済を指定
    'limit' => 50 // 取得件数を指定
];

// クエリパラメータの構築
$queryString = http_build_query($params);

// Fincode APIを呼び出して決済情報を検索
$apiUrl = FINCODE_API_ENDPOINT . '/v1/payments?' . $queryString;

// リクエストログにAPIパラメータを追加
$requestLog['api_params'] = $params;
$requestLog['api_url'] = $apiUrl;

// cURLセッションの初期化
$ch = curl_init($apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
// APIバージョンを指定しない場合は最新バージョンが使用される
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Authorization: Bearer ' . FINCODE_API_SECRET
    // 'Api-Version' ヘッダーを削除
]);

// リクエストの実行
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

// リクエストログにレスポンス情報を追加
$requestLog['http_status'] = $httpCode;
$requestLog['curl_error'] = $error;
$requestLog['response'] = $response;

// レスポンスをログに保存
$timestamp = date('YmdHis');
$logFile = $dataDir . '/payment_list_' . $timestamp . '.json';
$jsonLog = json_encode($requestLog, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
file_put_contents($logFile, $jsonLog);

// レスポンスの解析
$paymentData = json_decode($response, true);
```
