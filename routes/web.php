<?php

use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\GoogleController;
use App\Http\Controllers\LandingPageController;
use App\Http\Controllers\KomojuPaymentController;
use App\Http\Controllers\ContactFormController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\SalesController;
use App\Http\Controllers\Admin\StudentController;
use App\Http\Controllers\Admin\NewsletterController;
use App\Http\Controllers\Admin\ContactController;
use App\Http\Controllers\NewsletterSubscriptionController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use Illuminate\Http\Request;



Route::get('/', [LandingPageController::class, 'index'])->name('landing');


Auth::routes();

Route::get('/home', [HomeController::class, 'index'])->name('home');

// Google Login Routes
Route::get('login/google', [GoogleController::class, 'redirectToGoogle'])->name('login.google');

// Google Callback Route
Route::get('/auth/google/callback', function() {
    return app()->make(GoogleController::class)->handleGoogleCallback();
})->name('login.google.callback');

// テスト用ルート - 別のプレフィックス
Route::get('test/google/test', function() {
    return 'Google Auth Test Route is working!';
});

// コントローラーテスト用ルート - 別のプレフィックス
Route::get('test/google/controller-test', [GoogleController::class, 'handleGoogleCallback']);

// KOMOJU Payment Routes
Route::get('/payment/customer-info', [KomojuPaymentController::class, 'showCustomerInfoForm'])->name('payment.customer_info');
Route::post('/payment/store-customer-info', [KomojuPaymentController::class, 'storeCustomerInfo'])
	->withoutMiddleware([\App\Http\Middleware\VerifyCsrfToken::class])
	->name('payment.store_customer_info');
// 決済成功・キャンセルルート
Route::get('/payment/success', [KomojuPaymentController::class, 'success'])->name('payment.success');
Route::get('/payment/cancel', [KomojuPaymentController::class, 'cancel'])->name('payment.cancel');

// 旧ルート（互換性のために残しておく）
Route::post('/fincode/checkout', [KomojuPaymentController::class, 'showCustomerInfoForm'])->name('fincode.checkout');

// Static Pages Routes
Route::get('/privacy-policy', function () {
	return view('privacy-policy');
})->name('privacy-policy');

Route::get('/terms-of-service', function () {
	return view('terms-of-service');
})->name('terms-of-service');

Route::get('/tokusho', function () {
	return view('tokusho');
})->name('tokusho');

// お問い合わせルート
Route::get('/contact', [ContactFormController::class, 'index'])->name('contact.index');
Route::post('/contact', [ContactFormController::class, 'store'])->name('contact.store');
Route::get('/contact/complete', [ContactFormController::class, 'complete'])->name('contact.complete');

// メルマガ購読関連ルート
Route::get('/newsletter/subscribe/{formId}', [NewsletterSubscriptionController::class, 'showSubscriptionForm'])->name('newsletter.subscribe.form');
Route::post('/newsletter/subscribe/{formId}', [NewsletterSubscriptionController::class, 'subscribe'])->name('newsletter.subscribe');
Route::get('/newsletter/subscribed/{formId}', [NewsletterSubscriptionController::class, 'showSubscribedPage'])->name('newsletter.subscribed');
Route::get('/newsletter/unsubscribe/{token}', [NewsletterSubscriptionController::class, 'unsubscribe'])->name('newsletter.unsubscribe');
Route::get('/newsletter/{formId}', [NewsletterSubscriptionController::class, 'showLandingPage'])->name('newsletter.landing');

// 管理者用ルート
Route::prefix('admin')->middleware('admin')->name('admin.')->group(function () {
	// ダッシュボード
	Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

	// 売上管理
	Route::get('/sales', [SalesController::class, 'index'])->name('sales.index');
	Route::get('/sales/fincode', [SalesController::class, 'fincodePayments'])->name('sales.fincode');
	Route::get('/sales/{student}', [SalesController::class, 'show'])->name('sales.show');
	Route::delete('/sales/{student}', [SalesController::class, 'destroy'])->name('sales.destroy');
	Route::post('/sales/refund/{paymentId}', [SalesController::class, 'refund'])->name('sales.refund');

	// 受講者管理
	Route::get('/students', [StudentController::class, 'index'])->name('students.index');
	Route::get('/students/{student}', [StudentController::class, 'show'])->name('students.show');
	Route::get('/students/{student}/edit', [StudentController::class, 'edit'])->name('students.edit');
	Route::put('/students/{student}', [StudentController::class, 'update'])->name('students.update');
	Route::delete('/students/{student}', [StudentController::class, 'destroy'])->name('students.destroy');

	// メルマガ管理
	Route::get('/newsletter', [NewsletterController::class, 'index'])->name('newsletter.index');
	Route::get('/newsletter/create', [NewsletterController::class, 'create'])->name('newsletter.create');
	Route::post('/newsletter', [NewsletterController::class, 'store'])->name('newsletter.store');
	Route::get('/newsletter/{newsletter}/edit', [NewsletterController::class, 'edit'])->name('newsletter.edit');
	Route::put('/newsletter/{newsletter}', [NewsletterController::class, 'update'])->name('newsletter.update');
	Route::get('/newsletter/subscribers', [NewsletterController::class, 'subscribers'])->name('newsletter.subscribers');
	Route::post('/newsletter/subscribers', [NewsletterController::class, 'storeSubscriber'])->name('newsletter.subscribers.store');
	Route::put('/newsletter/subscribers/{subscriber}', [NewsletterController::class, 'updateSubscriber'])->name('newsletter.subscribers.update');
	Route::delete('/newsletter/subscribers/{subscriber}', [NewsletterController::class, 'destroySubscriber'])->name('newsletter.subscribers.destroy');
	Route::get('/newsletter/send', [NewsletterController::class, 'showSendForm'])->name('newsletter.send');
	Route::post('/newsletter/send', [NewsletterController::class, 'send'])->name('newsletter.send.post');
	Route::get('/newsletter/logs', [NewsletterController::class, 'logs'])->name('newsletter.logs');

	// お問い合わせ管理
	Route::get('/contacts', [ContactController::class, 'index'])->name('contacts.index');
	Route::get('/contacts/{contact}', [ContactController::class, 'show'])->name('contacts.show');
	Route::post('/contacts/{contact}/reply', [ContactController::class, 'reply'])->name('contacts.reply');
	Route::delete('/contacts/{contact}', [ContactController::class, 'destroy'])->name('contacts.destroy');
	Route::post('/contacts/fetch-emails', [ContactController::class, 'fetchEmailsFromImap'])->name('contacts.fetch-emails');
});
