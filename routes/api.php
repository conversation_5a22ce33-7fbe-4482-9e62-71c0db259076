<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\KomojuPaymentController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// テストルート
Route::post('/test', function (Request $request) {
    return response()->json([
        'message' => 'OK',
        'data' => $request->all()
    ]);
});

// KOMOJUコールバックルート
Route::post('/payment/success', [KomojuPaymentController::class, 'success'])->name('api.payment.success');
Route::post('/payment/cancel', [KomojuPaymentController::class, 'cancel'])->name('api.payment.cancel');
