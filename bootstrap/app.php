<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->validateCsrfTokens(except: [
            'fincode/checkout',
        ]);

        // 管理者ミドルウェアを登録
        $middleware->alias([
            'admin' => \App\Http\Middleware\AdminMiddleware::class,
        ]);

        // webミドルウェアグループを明示的に設定
        $middleware->web(append: [
            // 追加のミドルウェアがあればここに追加
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
