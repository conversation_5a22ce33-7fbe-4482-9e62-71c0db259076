<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
class CreateFaqsTable extends Migration
{
    public function up()
    {
        Schema::create('faqs', function (Blueprint $table) {
            $table->id();
            $table->string('question');
            $table->text('answer');
            $table->integer('order')->default(0); // 順番のカラム
            $table->string('category'); // カテゴリのカラム
            $table->timestamps(); // 作成日時と更新日時
        });
    }
    public function down()
    {
        Schema::dropIfExists('faqs');
    }
}
