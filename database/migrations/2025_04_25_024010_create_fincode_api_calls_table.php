<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fincode_api_calls', function (Blueprint $table) {
            $table->id();
            $table->string('type')->default('sales')->comment('API呼び出しのタイプ');
            $table->timestamp('last_called_at')->nullable()->comment('最後にAPIを呼び出した時刻');
            $table->integer('records_fetched')->default(0)->comment('取得したレコード数');
            $table->json('response_data')->nullable()->comment('最後のレスポンスデータ');
            $table->string('status')->default('success')->comment('最後の呼び出しのステータス');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fincode_api_calls');
    }
};
