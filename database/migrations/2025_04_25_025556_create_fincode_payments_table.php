<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fincode_payments', function (Blueprint $table) {
            $table->id();
            $table->string('payment_id')->unique()->comment('Fincode決済ID');
            $table->string('order_id')->nullable()->comment('注文ID');
            $table->string('shop_id')->nullable()->comment('ショップID');
            $table->string('access_id')->nullable()->comment('アクセスID');
            $table->string('transaction_id')->nullable()->comment('トランザクションID');
            $table->string('status')->comment('決済ステータス（UNPROCESSED, AUTHORIZED, CAPTURED, CANCELED等）');
            $table->string('pay_type')->comment('決済タイプ（Card, Paypay, konbini等）');
            $table->integer('amount')->default(0)->comment('決済金額');
            $table->string('card_no')->nullable()->comment('カード番号（マスク済み）');
            $table->string('brand')->nullable()->comment('カードブランド');
            $table->string('holder_name')->nullable()->comment('カード名義');
            $table->string('expire')->nullable()->comment('有効期限');
            $table->string('method')->nullable()->comment('支払い方法（1:一括払い, 2:分割払い等）');
            $table->string('pay_times')->nullable()->comment('分割回数');
            $table->string('job_code')->nullable()->comment('処理区分（AUTH, CAPTURE, SALES等）');
            $table->string('client_field_1')->nullable()->comment('加盟店自由項目1');
            $table->string('client_field_2')->nullable()->comment('加盟店自由項目2');
            $table->string('client_field_3')->nullable()->comment('加盟店自由項目3');
            $table->timestamp('process_date')->nullable()->comment('処理日時');
            $table->timestamp('created')->nullable()->comment('作成日時');
            $table->timestamp('updated')->nullable()->comment('更新日時');
            $table->json('payment_data')->nullable()->comment('決済データ（JSON形式）');
            $table->unsignedBigInteger('student_id')->nullable()->comment('関連する学生ID');
            $table->timestamps();

            // インデックス
            $table->index('status');
            $table->index('pay_type');
            $table->index('process_date');
            $table->index('order_id');
            $table->index('student_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fincode_payments');
    }
};
