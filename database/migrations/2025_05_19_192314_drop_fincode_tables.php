<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fincodeの決済APIコール履歴テーブルを削除
        Schema::dropIfExists('fincode_api_calls');

        // Fincodeの決済情報テーブルを削除
        Schema::dropIfExists('fincode_payments');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // このマイグレーションはテーブルを削除するだけなので、
        // down()メソッドでは何もしません。
        // 必要に応じて、テーブルを再作成するマイグレーションを実行してください。
    }
};
