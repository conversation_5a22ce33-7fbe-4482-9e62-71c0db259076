<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('komoju_payments', function (Blueprint $table) {
            $table->id();
            $table->string('session_id')->nullable()->comment('KOMOJUセッションID');
            $table->string('payment_id')->nullable()->comment('KOMOJU決済ID');
            $table->string('order_id')->nullable()->comment('注文ID');
            $table->string('status')->nullable()->comment('決済ステータス');
            $table->integer('amount')->nullable()->comment('決済金額');
            $table->string('currency')->default('JPY')->comment('通貨');
            $table->string('payment_method')->nullable()->comment('決済方法');
            $table->string('customer_name')->nullable()->comment('顧客名');
            $table->string('customer_email')->nullable()->comment('顧客メールアドレス');
            $table->foreignId('student_id')->nullable()->comment('学生ID')
                ->constrained()->onDelete('set null');
            $table->json('payment_data')->nullable()->comment('決済データ（JSON）');
            $table->timestamps();

            // インデックス
            $table->index('session_id');
            $table->index('payment_id');
            $table->index('order_id');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('komoju_payments');
    }
};
