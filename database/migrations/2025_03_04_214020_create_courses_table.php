<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('courses', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->integer('price');
            $table->string('level');
            $table->timestamps();
        });

        // 初期データの挿入
        DB::table('courses')->insert([
            ['name' => '楽しく学ぶタロット講座 初級編', 'description' => '初級者向けのタロット講座です。', 'price' => 19800, 'level' => '初級', 'created_at' => now(), 'updated_at' => now()],
            ['name' => '楽しく学ぶタロット講座 中級編', 'description' => '中級者向けのタロット講座です。', 'price' => 29800, 'level' => '中級', 'created_at' => now(), 'updated_at' => now()],
            ['name' => '楽しく学ぶタロット講座 上級編', 'description' => '上級者向けのタロット講座です。', 'price' => 39800, 'level' => '上級', 'created_at' => now(), 'updated_at' => now()],
            ['name' => '楽しく学ぶタロット講座 初級+中級セット', 'description' => '初級と中級のセット講座です。', 'price' => 44800, 'level' => '初級+中級', 'created_at' => now(), 'updated_at' => now()],
            ['name' => '楽しく学ぶタロット講座 中級+上級セット', 'description' => '中級と上級のセット講座です。', 'price' => 64800, 'level' => '中級+上級', 'created_at' => now(), 'updated_at' => now()],
            ['name' => '楽しく学ぶタロット講座 全講座セット', 'description' => '全講座のセットです。', 'price' => 79800, 'level' => '全講座', 'created_at' => now(), 'updated_at' => now()],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('courses');
    }
};
