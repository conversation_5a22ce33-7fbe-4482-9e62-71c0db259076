<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('students', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email');
            $table->string('phone')->nullable();
            $table->text('address')->nullable();
            $table->string('order_id')->unique();
            $table->foreignId('course_id')->constrained()->onDelete('cascade');
            $table->string('payment_status')->default('pending'); // pending, completed, failed, refunded
            $table->string('payment_id')->nullable();
            $table->integer('amount');
            $table->json('payment_response')->nullable(); // Fincodeからのレスポンスデータを保存
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('students');
    }
};
