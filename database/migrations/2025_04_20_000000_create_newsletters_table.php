<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('newsletters', function (Blueprint $table) {
            $table->id();
            $table->string('title');                           // ニュースレターのタイトル
            $table->string('form_id')->unique();              // 登録フォームのユニークID
            $table->string('image_path')->nullable();         // アイキャッチ画像のパス
            $table->string('gift_file_path')->nullable();     // 登録者プレゼントのファイルパス
            $table->text('gift_description')->nullable();     // プレゼントの説明
            $table->longText('sales_letter')->nullable();     // セールスレター（登録推奨文）
            $table->string('video_url')->nullable();          // YouTube動画URL
            $table->string('video_file_path')->nullable();    // 動画ファイルパス
            $table->string('landing_page_url')->nullable();   // ランディングページのURL
            $table->text('content')->nullable();              // メール本文
            $table->timestamp('sent_at')->nullable();         // 送信日時
            $table->integer('recipients_count')->default(0);  // 送信数
            $table->boolean('is_active')->default(true);      // アクティブ状態
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('newsletters');
    }
};
