@extends('layouts.payment-redirect')

@section('content')
    <!-- このページは自動的にリダイレクトされます -->
    <!-- JSONデータをJavaScriptで処理してリダイレクトします -->
    <div id="payment-data" data-json='{{ json_encode($data) }}' style="display: none;"></div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // JSONデータを取得
        const dataElement = document.getElementById('payment-data');
        if (dataElement) {
            try {
                const responseData = JSON.parse(dataElement.getAttribute('data-json'));

                // 成功レスポンスの場合、リダイレクトURLがあればリダイレクト
                if (responseData.success && responseData.redirect_url) {
                    console.log('Payment successful, redirecting to:', responseData.redirect_url);
                    window.location.href = responseData.redirect_url;
                } else if (!responseData.success && responseData.redirect_url) {
                    console.error('Payment error:', responseData.message);
                    alert('決済処理中にエラーが発生しました: ' + responseData.message);
                    window.location.href = responseData.redirect_url;
                } else {
                    // リダイレクトURLがない場合はトップページに戻る
                    console.error('No redirect URL found');
                    window.location.href = '/';
                }
            } catch (e) {
                console.error('Error parsing JSON data:', e);
                window.location.href = '/';
            }
        } else {
            console.error('Payment data element not found');
            window.location.href = '/';
        }
    });
</script>
@endsection
