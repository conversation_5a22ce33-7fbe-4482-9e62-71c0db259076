@extends('layouts.app')

@section('content')
<div class="container py-5">
	<div class="row justify-content-center">
		<div class="col-md-8">
			<div class="card">
				<div class="card-header bg-primary text-white">
					<h4 class="mb-0">購入者情報入力</h4>
				</div>
				<div class="card-body">
					@if ($errors->any())
						<div class="alert alert-danger">
							<ul class="mb-0">
								@foreach ($errors->all() as $error)
									<li>{{ $error }}</li>
								@endforeach
							</ul>
						</div>
					@endif

					<div class="mb-4">
						<h5>購入コース: {{ $course->name }}</h5>
						<p class="text-muted">価格: {{ number_format($course->price) }}円（税込）</p>
					</div>

					<form method="POST" action="{{ route('payment.store_customer_info') }}">
						@csrf
						<!-- CSRFトークンを明示的に追加 -->
						<input type="hidden" name="_token" value="{{ csrf_token() }}">
						<!-- セッションIDを追加 -->
						<input type="hidden" name="_session_id" value="{{ session()->getId() }}">

						<input type="hidden" name="course_id" value="{{ $course->id }}">

						<div class="form-group mb-3">
							<label for="name">お名前 <span class="text-danger">*</span></label>
							<input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name') }}" required>
							@error('name')
								<span class="invalid-feedback" role="alert">
									<strong>{{ $message }}</strong>
								</span>
							@enderror
						</div>

						<div class="form-group mb-3">
							<label for="email">メールアドレス <span class="text-danger">*</span></label>
							<input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email') }}" required>
							<small class="form-text text-muted">購入完了メールの送信先になります。正確に入力してください。</small>
							@error('email')
								<span class="invalid-feedback" role="alert">
									<strong>{{ $message }}</strong>
								</span>
							@enderror
						</div>

						<div class="form-group mb-3">
							<label for="phone">電話番号</label>
							<input type="tel" class="form-control @error('phone') is-invalid @enderror" id="phone" name="phone" value="{{ old('phone') }}">
							<small class="form-text text-muted">ハイフンなしで入力してください（例: 09012345678）</small>
							@error('phone')
								<span class="invalid-feedback" role="alert">
									<strong>{{ $message }}</strong>
								</span>
							@enderror
						</div>
						<div class="form-check mb-4">
							<input class="form-check-input @error('terms') is-invalid @enderror" type="checkbox" id="terms" name="terms" required>
							<label class="form-check-label" for="terms">
								<a href="#" data-toggle="modal" data-target="#termsOfServiceModal">利用規約</a>および<a href="#" data-toggle="modal" data-target="#privacyPolicyModal">プライバシーポリシー</a>に同意します
							</label>
							@error('terms')
								<span class="invalid-feedback" role="alert">
									<strong>{{ $message }}</strong>
								</span>
							@enderror
						</div>

						<div class="d-grid">
							<button type="submit" class="btn btn-primary btn-lg">決済ページへ進む</button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>
@endsection
