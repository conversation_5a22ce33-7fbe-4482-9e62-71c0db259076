@extends('layouts.app')

@section('content')
<div class="container py-5">
	<div class="row justify-content-center">
		<div class="col-md-8">
			<div class="card">
				<div class="card-header bg-success text-white">
					<h4 class="mb-0">購入完了</h4>
				</div>
				<div class="card-body text-center">
					<div class="mb-4">
						<i class="fas fa-check-circle text-success" style="font-size: 5rem;"></i>
					</div>

					<h3 class="mb-3">ご購入ありがとうございます！</h3>

					<p class="lead mb-4">{{ $student->course->name }}の購入が完了しました。</p>

					<div class="card mb-4">
						<div class="card-body">
							<h5 class="card-title">購入情報</h5>
							<table class="table table-borderless">
								<tr>
									<th class="text-start">注文番号:</th>
									<td class="text-start">{{ $student->order_id }}</td>
								</tr>
								<tr>
									<th class="text-start">コース名:</th>
									<td class="text-start">{{ $student->course->name }}</td>
								</tr>
								<tr>
									<th class="text-start">金額:</th>
									<td class="text-start">{{ number_format($student->amount) }}円（税込）</td>
								</tr>
								<tr>
									<th class="text-start">購入日:</th>
									<td class="text-start">{{ $student->updated_at->format('Y年m月d日 H:i') }}</td>
								</tr>
							</table>
						</div>
					</div>

					<p>登録メールアドレス: {{ $student->email }} に購入完了メールを送信しました。</p>
					<p class="text-muted small">ご不明な点があればお問い合わせください。</p>

				</div>
			</div>
		</div>
	</div>
</div>
@endsection
