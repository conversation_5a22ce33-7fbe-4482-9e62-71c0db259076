@extends('layouts.app')

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">決済が完了しました</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        お支払いありがとうございます。決済が正常に完了しました。
                    </div>

                    <div class="mb-4">
                        <h5>決済情報</h5>
                        <p><strong>注文ID:</strong> {{ $order_id }}</p>
                        <p><strong>コース名:</strong> {{ $course_name }}</p>
                        <p><strong>金額:</strong> {{ number_format($amount) }}円</p>
                        <p><strong>決済ID:</strong> {{ $payment_id }}</p>
                    </div>

                    @if(auth()->check())
                        <div class="alert alert-info">
                            コースへのアクセス権が付与されました。マイページからコースにアクセスできます。
                        </div>
                    @else
                        <div class="alert alert-warning">
                            ログインしていない状態で購入されました。アカウントをお持ちの方はログインして、マイページからコースにアクセスしてください。
                            <br>アカウントをお持ちでない方は、登録後にサポートまでご連絡ください。
                        </div>
                    @endif

                    <div class="d-grid gap-2">
                        <a href="{{ route('landing') }}" class="btn btn-primary">ホームに戻る</a>
                        @if(auth()->check())
                            <a href="{{ route('home') }}" class="btn btn-outline-primary">マイページへ</a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
