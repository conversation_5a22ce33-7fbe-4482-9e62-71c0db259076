<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $newsletter->title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            color: #333;
            font-family: 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
        }
        .landing-container {
            max-width: 900px;
            margin: 50px auto;
            padding: 30px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .landing-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .landing-image {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .btn-primary {
            background-color: #6c757d;
            border-color: #6c757d;
            padding: 12px 24px;
            font-size: 1.1rem;
        }
        .btn-primary:hover {
            background-color: #5a6268;
            border-color: #5a6268;
        }
        .gift-description {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .video-container {
            margin-bottom: 30px;
        }
        .sales-letter {
            line-height: 1.8;
            margin-bottom: 30px;
        }
        .sales-letter h2, .sales-letter h3 {
            margin-top: 1.5rem;
            margin-bottom: 1rem;
        }
        .sales-letter ul, .sales-letter ol {
            margin-bottom: 1.5rem;
        }
        .cta-section {
            text-align: center;
            margin: 40px 0;
            padding: 30px;
            background-color: #f8f9fa;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="landing-container">
            <div class="landing-header">
                <h1>{{ $newsletter->title }}</h1>
            </div>

            @if($newsletter->image_path)
                <div class="text-center">
                    <img src="{{ asset('storage/' . $newsletter->image_path) }}" alt="{{ $newsletter->title }}" class="landing-image">
                </div>
            @endif

            @if($newsletter->video_url || $newsletter->video_file_path)
                <div class="video-container">
                    @if($newsletter->video_url)
                        <div class="ratio ratio-16x9">
                            <iframe src="{{ $newsletter->video_url }}" title="{{ $newsletter->title }}" allowfullscreen></iframe>
                        </div>
                    @elseif($newsletter->video_file_path)
                        <div class="ratio ratio-16x9">
                            <video controls>
                                <source src="{{ asset('storage/' . $newsletter->video_file_path) }}" type="video/mp4">
                                お使いのブラウザは動画再生に対応していません。
                            </video>
                        </div>
                    @endif
                </div>
            @endif

            @if($newsletter->sales_letter)
                <div class="sales-letter">
                    {!! $newsletter->sales_letter !!}
                </div>
            @endif

            <div class="cta-section">
                @if($newsletter->gift_description)
                    <div class="gift-description mb-4">
                        <h4><i class="fas fa-gift me-2"></i>登録プレゼント</h4>
                        <div class="mt-3">
                            {!! nl2br(e($newsletter->gift_description)) !!}
                        </div>
                    </div>
                @endif

                <h3 class="mb-4">今すぐメルマガに登録する</h3>
                <a href="{{ route('newsletter.subscribe.form', ['formId' => $formId]) }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-paper-plane me-2"></i>無料登録はこちら
                </a>
                <p class="mt-3 text-muted">
                    ※ 登録は無料です。いつでも解除できます。
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
