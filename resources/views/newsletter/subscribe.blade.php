<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $newsletter->title }} - メルマガ登録</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            color: #333;
            font-family: 'Helvetica Neue', Arial, sans-serif;
        }
        .newsletter-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .newsletter-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .newsletter-image {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .form-control:focus {
            border-color: #6c757d;
            box-shadow: 0 0 0 0.25rem rgba(108, 117, 125, 0.25);
        }
        .btn-primary {
            background-color: #6c757d;
            border-color: #6c757d;
        }
        .btn-primary:hover {
            background-color: #5a6268;
            border-color: #5a6268;
        }
        .gift-description {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .video-container {
            margin-bottom: 30px;
        }
        .sales-letter {
            line-height: 1.8;
            margin-bottom: 30px;
        }
        .sales-letter h2, .sales-letter h3 {
            margin-top: 1.5rem;
            margin-bottom: 1rem;
        }
        .sales-letter ul, .sales-letter ol {
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="newsletter-container">
            <div class="newsletter-header">
                <h1>{{ $newsletter->title }}</h1>
                <p class="text-muted">メルマガ登録フォーム</p>
            </div>

            @if(session('error'))
                <div class="alert alert-danger">
                    {{ session('error') }}
                </div>
            @endif

            @if($newsletter->image_path)
                <div class="text-center">
                    <img src="{{ asset('storage/' . $newsletter->image_path) }}" alt="{{ $newsletter->title }}" class="newsletter-image">
                </div>
            @endif

            @if($newsletter->video_url || $newsletter->video_file_path)
                <div class="video-container mb-4">
                    @if($newsletter->video_url)
                        <div class="ratio ratio-16x9">
                            <iframe src="{{ $newsletter->video_url }}" title="{{ $newsletter->title }}" allowfullscreen></iframe>
                        </div>
                    @elseif($newsletter->video_file_path)
                        <div class="ratio ratio-16x9">
                            <video controls>
                                <source src="{{ asset('storage/' . $newsletter->video_file_path) }}" type="video/mp4">
                                お使いのブラウザは動画再生に対応していません。
                            </video>
                        </div>
                    @endif
                </div>
            @endif

            @if($newsletter->sales_letter)
                <div class="sales-letter mb-4">
                    {!! $newsletter->sales_letter !!}
                </div>
            @endif

            @if($newsletter->gift_description)
                <div class="gift-description">
                    <h4><i class="fas fa-gift me-2"></i>登録プレゼント</h4>
                    <div class="mt-3">
                        {!! nl2br(e($newsletter->gift_description)) !!}
                    </div>
                </div>
            @endif

            <form action="{{ route('newsletter.subscribe', ['formId' => $formId]) }}" method="POST">
                @csrf

                <div class="mb-3">
                    <label for="name" class="form-label">お名前</label>
                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name') }}" required>
                    @error('name')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="email" class="form-label">メールアドレス</label>
                    <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email') }}" required>
                    @error('email')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="agree" name="agree" required>
                    <label class="form-check-label" for="agree">
                        <a href="{{ route('privacy-policy') }}" target="_blank">プライバシーポリシー</a>に同意します
                    </label>
                </div>

                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-paper-plane me-2"></i>メルマガに登録する
                    </button>
                </div>

                <div class="mt-3 text-center text-muted small">
                    <p>※ 登録後、プレゼントのダウンロードリンクをメールでお送りします。</p>
                    <p>※ いつでも購読解除できます。</p>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
