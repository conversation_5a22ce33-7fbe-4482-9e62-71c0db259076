<!DOCTYPE html>
<html lang="ja">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<!-- CSRF Token -->
	<meta name="csrf-token" content="{{ csrf_token() }}">
	<title>世界一受けたいタロット講座 初級編 | Tarotique</title>
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
	<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@300;400;500;700&display=swap" rel="stylesheet">
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/mplus1p@1.0.0/dist/mplus1p.min.css">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
	<link rel="stylesheet" href="land2.css">
	<style>
		/* モーダルスタイル調整 */
		.modal-body {
			max-height: 70vh;
			overflow-y: auto;
		}
		.modal-body .list-group-item {
			padding: 0.75rem 1.25rem;
			background-color: transparent;
			border: none;
			border-bottom: 1px solid rgba(0,0,0,.125);
		}
		.modal-body .list-group-flush {
			margin-left: 1rem;
		}
		.modal-body section {
			padding-left: 0.5rem;
			padding-right: 0.5rem;
		}
	</style>
</head>
<body>
	<header>
		<div class="container">
			<div class="logo">
				<h1>Tarotique</h1>
			</div>
			<nav>
				<ul>
					<li><a href="#features">講座の特徴</a></li>
					<li><a href="#instructor">講師紹介</a></li>
					<li><a href="#contents">コンテンツ</a></li>
					<li><a href="#price">料金</a></li>
				</ul>
			</nav>
		</div>
	</header>

	<!-- メッセージ表示 -->
	@if(session('cancel'))
	<div class="container mt-3">
		<div class="alert alert-warning alert-dismissible fade show" role="alert">
			<strong>{{ session('cancel') }}</strong>
			<button type="button" class="close" data-dismiss="alert" aria-label="Close">
				<span aria-hidden="true">&times;</span>
			</button>
		</div>
	</div>
	@endif

	@if(session('payment_incomplete'))
	<div class="container mt-3">
		<div class="alert alert-info alert-dismissible fade show" role="alert">
			<strong>{{ session('payment_incomplete') }}</strong>
			<button type="button" class="close" data-dismiss="alert" aria-label="Close">
				<span aria-hidden="true">&times;</span>
			</button>
		</div>
	</div>
	@endif

	<section id="hero" class="hero">
		<div class="container">
			<div class="hero-content">
				<h1>世界一受けたいタロット講座 初級編</h1>
				<p class="subtitle">実践重視！挫折させない、本気のタロット講座</p>
				<div class="hero-text">
					<p>あなたはタロットを学びたいと思いながら、どこから始めればいいのか迷っていませんか？</p>
					<p>書籍や情報があふれる中、何が正しいのかわからず、混乱した経験はありませんか？</p>
					<p>私は12年のタロット経験から、最短で確実にタロットをマスターできる道筋を見つけました。</p>
					<p>このノウハウを、実践に特化した独自のメソッドとしてお届けします。</p>
				</div>
				<div class="cta-button">
					<a href="{{ route('payment.customer_info', ['course_id' => $course->id]) }}" class="btn btn-primary">この講座を受講する</a>
				</div>
			</div>
			<div class="hero-image">
				<img src="{{ asset('img/tarots.avif') }}" alt="タロットカードを広げる女性の手元 [600x400]">
			</div>
		</div>
	</section>

	<section id="features" class="features">
		<div class="container">
			<h2 class="section-title">講座の特徴</h2>
			<div class="features-grid">
				<div class="feature-card">
					<div class="feature-icon">
						<i class="fas fa-hands"></i>
					</div>
					<h3>実践重視の内容</h3>
					<p>知識だけでなく、実際の鑑定現場で使えるスキルを養成します。</p>
				</div>
				<div class="feature-card">
					<div class="feature-icon">
						<i class="fas fa-book-open"></i>
					</div>
					<h3>体系的な学習</h3>
					<p>初心者でも迷わない、ステップバイステップの学習プログラム。</p>
				</div>
				<div class="feature-card">
					<div class="feature-icon">
						<i class="fas fa-comments"></i>
					</div>
					<h3>実践的フィードバック</h3>
					<p>あなたのリーディングに対する的確なアドバイスで成長を促進。</p>
				</div>
				<div class="feature-card">
					<div class="feature-icon">
						<i class="fas fa-file-alt"></i>
					</div>
					<h3>効率的なヒアリングシート</h3>
					<p>相談者の本音を引き出し、満足度の高い鑑定につなげるツール提供。</p>
				</div>
				<div class="feature-card">
					<div class="feature-icon">
						<i class="fas fa-envelope"></i>
					</div>
					<h3>定期的な学習確認</h3>
					<p>メールでの進捗確認で、着実に力をつけていきます。</p>
				</div>
				<div class="feature-card">
					<div class="feature-icon">
						<i class="fas fa-user-friends"></i>
					</div>
					<h3>個別セッションオプション</h3>
					<p>わからない部分は個別セッションでフォロー（別料金）。</p>
				</div>
			</div>
		</div>
	</section>

	<section id="techniques" class="techniques">
		<div class="container">
			<h2 class="section-title">相談者に寄り添ったリーディングテクニック</h2>
			<div class="techniques-container">
				<div class="technique-image">
					<img src="{{ asset('img/reading.png') }}" alt="タロットリーディングの様子 [600x400]">
				</div>
				<div class="technique-content">
					<div class="technique-item">
						<i class="fas fa-star"></i>
						<p>相談者が前のめりになる質問テクニック</p>
					</div>
					<div class="technique-item">
						<i class="fas fa-redo"></i>
						<p>リピートを生み出す信頼関係の構築法</p>
					</div>
					<div class="technique-item">
						<i class="fas fa-users"></i>
						<p>実績を積み上げるための効果的な集客方法</p>
					</div>
					<div class="quote">
						<p>「タロットは単なるカード占いではなく、相談者の人生に寄り添うツールです」</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<section id="instructor" class="instructor">
		<div class="container">
			<h2 class="section-title">講師紹介</h2>
			<div class="instructor-profile">
				<div class="instructor-image">
					<img src="img/davis.jpg" alt="で〜びす金星王 講師プロフィール写真 [400x400]">
				</div>
				<div class="instructor-info">
					<h3>で〜びす金星王</h3>
					<p class="instructor-title">タロット鑑定師・講師</p>
					<div class="instructor-achievements">
						<div class="achievement">
							<i class="fas fa-trophy"></i>
							<p>チャット占いで月間500鑑定</p>
						</div>
						<div class="achievement">
							<i class="fas fa-envelope-open-text"></i>
							<p>メール占い他 月間90鑑定</p>
						</div>
						<div class="achievement">
							<i class="fas fa-chalkboard-teacher"></i>
							<p>途切れない予約と信頼を得て現在 占い講師として活動中</p>
						</div>
					</div>
					<p class="instructor-message">
						「タロットの世界は奥深く、魅力に満ちています。私の経験と知識を活かし、あなたがタロットを通じて人々に寄り添える占い師になるお手伝いをします。」
					</p>
				</div>
			</div>
		</div>
	</section>

	<section id="contents" class="contents">
		<div class="container">
			<h2 class="section-title">学習コンテンツ</h2>
			<div class="contents-image">
				<img src="img/image.png" alt="タロット学習コンテンツ">
			</div>
			<div class="contents-grid">
				<div class="content-card">
					<div class="content-icon">
						<i class="fas fa-file-pdf"></i>
					</div>
					<div class="content-info">
						<h3>講座テキスト (PDF)</h3>
						<p>約19,000文字の大ボリューム。タロットの基礎から実践まで網羅。</p>
					</div>
				</div>
				<div class="content-card">
					<div class="content-icon">
						<i class="fas fa-video"></i>
					</div>
					<div class="content-info">
						<h3>実践タロットリーディング動画</h3>
						<p>プロによる実際のリーディングを16分の動画で解説。</p>
					</div>
				</div>
				<div class="content-card">
					<div class="content-icon">
						<i class="fas fa-play-circle"></i>
					</div>
					<div class="content-info">
						<h3>タロット講習動画</h3>
						<p>26分の解説動画で効率的に学ぶタロットの世界。</p>
					</div>
				</div>
				<div class="content-card">
					<div class="content-icon">
						<i class="fas fa-clipboard-list"></i>
					</div>
					<div class="content-info">
						<h3>ヒアリングシート(PDF)</h3>
						<p>顧客リピートに直結する効果的な質問シート。</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<section id="session" class="session">
		<div class="container">
			<h2 class="section-title">タロット鑑定セッション</h2>
			<div class="session-image">
				<img src="img/session_woman.png" alt="タロット鑑定セッション">
			</div>
			<p class="session-intro">
				理論は理解できても、実際のリーディングとなると不安がよぎる…。「これでいいのかな？」「この解釈は正しいのだろうか」という疑問を講師とマンツーマンで答え合わせ。
				当セッションは、そんなあなたの「知識」を「確かな実践力」へと変える特別な40分です。
			</p>

			<div class="session-details">
				<div class="session-info">
					<div class="session-price">
						<span class="price-label">セッション料金</span>
						<span class="duration-value">40分</span> <span class="price-value">3,500円</span>
					</div>
				</div>

				<h3 class="features-title">セッションの特徴</h3>
				<div class="session-features">
					<div class="session-feature">
						<h4>プロの講師と共にリーディング</h4>
						<p>プロの講師があなたと共にリーディングを行い、一枚一枚のカードの解釈から全体の流れまで、丁寧にフィードバック。「なぜこの解釈になるのか」「どうすればより的確なリーディングができるのか」を実践を通して学べます。</p>
					</div>
					<div class="session-feature">
						<h4>実践だからこそ見えるポイント</h4>
						<p>教材だけではつかみきれなかった繊細なニュアンスや、カードの組み合わせが持つ深い意味、リーディングの流れなど、実践だからこそ見えてくる大切なポイントを講師と共に掘り下げていきます。</p>
					</div>
					<div class="session-feature">
						<h4>スキルの進化</h4>
						<p>このセッションを受けることで、あなたのタロット鑑定は単なる「知識の集積」から「生きたスキル」へと進化します。疑問点が解消され、自分の感覚を信じる力が育まれ、一人でリーディングを行う際の自信につながります。</p>
					</div>
				</div>

				<div class="session-closing">
					<p>タロットの奥深い世界をより自由に、より確かに探求するための大切な一歩。あなたのタロットジャーニーを、プロの視点と温かなサポートで後押しします。</p>
					<p>鑑定の答え合わせから、解釈の深掘り、テクニックの磨き方まで あなたのタロットスキルを次のレベルへ導く40分をぜひ体験してください。</p>
				</div>
			</div>
		</div>
	</section>

	<section id="recommendations" class="recommendations">
		<div class="container">
			<h2 class="section-title">こんな方におすすめです</h2>
			<div class="recommendations-list">
				<div class="recommendation-item">
					<div class="recommendation-icon">
						<i class="fas fa-heart"></i>
					</div>
					<p>タロットを本気でマスターしたい強い意志をお持ちの方</p>
				</div>
				<div class="recommendation-item">
					<div class="recommendation-icon">
						<i class="fas fa-running"></i>
					</div>
					<p>積極的に実践に取り組める方</p>
				</div>
				<div class="recommendation-item">
					<div class="recommendation-icon">
						<i class="fas fa-smile"></i>
					</div>
					<p>素直な気持ちで学びに向き合える方</p>
				</div>
			</div>
		</div>
	</section>

	<section id="steps" class="steps">
		<div class="container">
			<h2 class="section-title">学習ステップ</h2>
			<div class="steps-image">
				<img src="img/mother.avif" alt="タロット学習の様子">
			</div>
			<div class="steps-timeline">
				<div class="step">
					<div class="step-number">1</div>
					<div class="step-content">
						<h3>基礎知識の習得</h3>
						<p>タロットカードの基本と意味を学ぶ</p>
					</div>
				</div>
				<div class="step">
					<div class="step-number">2</div>
					<div class="step-content">
						<h3>実践トレーニング</h3>
						<p>実際のカードを使った読み方の練習</p>
					</div>
				</div>
				<div class="step">
					<div class="step-number">3</div>
					<div class="step-content">
						<h3>フィードバック</h3>
						<p>講師からの実践的なアドバイス</p>
					</div>
				</div>
				<div class="step">
					<div class="step-number">4</div>
					<div class="step-content">
						<h3>実践鑑定</h3>
						<p>SNSやプラットフォームを利用して実践鑑定</p>
					</div>
				</div>
				<div class="step">
					<div class="step-number">5</div>
					<div class="step-content">
						<h3>スキル向上</h3>
						<p>スキルに自信がつく</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<section id="faq" class="faq">
		<div class="container">
			<h2 class="section-title">よくある質問</h2>
			<div class="faq-container">
				@if(isset($faqs) && $faqs->count() > 0)
					@foreach($faqs as $faq)
						<div class="faq-item">
							<div class="faq-question">
								<h3>{{ $faq->question }}</h3>
								<div class="faq-toggle">
									<i class="fas fa-plus"></i>
								</div>
							</div>
							<div class="faq-answer">
								{!! nl2br(e($faq->answer)) !!}
							</div>
						</div>
					@endforeach
				@else
					<p>現在、よくある質問はありません。</p>
				@endif
			</div>
		</div>
	</section>

	<section id="price" class="price">
		<div class="container">
			<h2 class="section-title">講座料金</h2>
			<div class="price-card" style="text-align: center;">
				<div class="price-header">
					<h3>世界一受けたいタロット講座 初級編</h3>
				</div>
				<div class="price-amount">
					<span class="price-value">{{ number_format($course->price) }}</span>
					<span class="price-currency">円(税込)</span>
				</div>
				<div class="price-includes">
					<p>含まれるもの：</p>
					<ul>
						<li>講座テキスト (PDF) 約19000文字</li>
						<li>実践タロットリーディング動画 16分</li>
						<li>タロット講習動画 26分</li>
						<li>顧客リピートに役立つヒアリングシート(PDF)</li>
					</ul>
				</div>

			<div class="cta-button" style="text-align: center; padding-bottom: 1.5rem;">
					<a href="{{ route('payment.customer_info', ['course_id' => $course->id]) }}" class="btn btn-primary">この講座を受講する</a>
				</div>
		</div>
		</div>
	</section>

	<!-- 下部CTA -->

	<section id="application" class="cta-section">
		<div class="container">
			<div class="cta-content">
				<h2>「知識」だけでなく「実践力」を身につけたい。</h2>
				<p>タロットをリアルタイムでスラスラ読める楽しみを体験して下さい。</p>
				<div class="cta-statistic">
					<p>受講者の74%が4ヶ月以内に実践的なリーディングスキルを習得</p>
				</div>
				<div class="cta-button">
					<a href="{{ route('payment.customer_info', ['course_id' => $course->id]) }}" class="btn btn-primary">この講座を受講する</a>
				</div>
			</div>
		</div>
	</section>
	<!-- エラーメッセージ表示用の要素 -->
	<div id="error-message" class="error-message" style="display: none; color: red;"></div>

<footer class="bg-dark text-white py-4">
	<div class="container-lg">
		<div class="row">
			<div class="col-md-6 mb-3 mb-md-0">
				<h5>Tarotique</h5>
				<p class="small">タロットカードの世界へようこそ</p>
			</div>
			<div class="col-md-6 text-md-right">
				<div class="mb-3">
					<a href="{{ url('/') }}" class="text-white mx-2">HOME</a> |
					<a href="{{ url('/') }}" class="text-white mx-2">講座</a> |
					{{-- <a href="{{ url("faq") }}" class="text-white mx-2">FAQ</a> | --}}
					<a href="{{ url("tokusho") }}" class="text-white mx-2">特定商取引法に基づく表記</a> |
					<a href="#" class="text-white mx-2" data-toggle="modal" data-target="#privacyPolicyModal">プライバシーポリシー</a> |
					<a href="#" class="text-white mx-2" data-toggle="modal" data-target="#termsOfServiceModal">利用規約</a> |
					<a href="{{ url('contact') }}" class="text-white mx-2">お問い合わせ</a>
				</div>
				<p class="small">&copy; {{ date('Y') }} Tarotique. All rights reserved.</p>
			</div>
		</div>
	</div>
</footer>

<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.0.7/dist/umd/popper.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

<script>
	// フラッシュメッセージを5秒後に自動的に消す
	$(document).ready(function() {
		setTimeout(function() {
			$('.alert').fadeOut('slow');
		}, 5000);
	});
</script>

	<script>
		document.querySelectorAll('.faq-question').forEach(question => {
			question.addEventListener('click', () => {
				const answer = question.nextElementSibling;
				const toggle = question.querySelector('.faq-toggle i');

				if (answer.style.maxHeight) {
					answer.style.maxHeight = null;
					toggle.classList.remove('fa-minus');
					toggle.classList.add('fa-plus');
				} else {
					answer.style.maxHeight = answer.scrollHeight + 'px';
					toggle.classList.remove('fa-plus');
					toggle.classList.add('fa-minus');
				}
			});
		});
	</script>
<!-- エラーメッセージ表示用スクリプト -->
<script>
	window.addEventListener("pageshow", function (event) {
		if (event.persisted) {
			window.location.reload();
		}
	});

	document.addEventListener('DOMContentLoaded', function() {
		function showError(message) {
			const errorElement = document.getElementById('error-message');
			if (errorElement) {
				errorElement.textContent = message;
				errorElement.style.display = 'block';
			} else {
				alert(message);
			}
		}
	});
</script>
	<!-- プライバシーポリシーモーダル -->
<div class="modal fade" id="privacyPolicyModal" tabindex="-1" role="dialog" aria-labelledby="privacyPolicyModalLabel" aria-hidden="true">
	<div class="modal-dialog modal-lg" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="privacyPolicyModalLabel">プライバシーポリシー</h5>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div class="modal-body">
				{!! $privacyPolicyContent !!}
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal">閉じる</button>
			</div>
		</div>
	</div>
</div>

<!-- 利用規約モーダル -->
<div class="modal fade" id="termsOfServiceModal" tabindex="-1" role="dialog" aria-labelledby="termsOfServiceModalLabel" aria-hidden="true">
	<div class="modal-dialog modal-lg" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="termsOfServiceModalLabel">利用規約</h5>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div class="modal-body">
				{!! $termsOfServiceContent !!}
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal">閉じる</button>
			</div>
		</div>
	</div>
</div>

</body>
</html>
