@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>お問い合わせ一覧</h1>
        <form action="{{ route('admin.contacts.fetch-emails') }}" method="POST">
            @csrf
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-sync-alt me-1"></i> メールを取得
            </button>
        </form>
    </div>

    <!-- 検索・フィルター -->
    <div class="card mb-4">
        <div class="card-body">
            <form action="{{ route('admin.contacts.index') }}" method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">検索</label>
                    <input type="text" class="form-control" id="search" name="search" value="{{ request('search') }}" placeholder="名前、メール、件名">
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">ステータス</label>
                    <select class="form-select" id="status" name="status">
                        <option value="all" {{ request('status') == 'all' ? 'selected' : '' }}>すべて</option>
                        <option value="new" {{ request('status') == 'new' ? 'selected' : '' }}>新規</option>
                        <option value="replied" {{ request('status') == 'replied' ? 'selected' : '' }}>返信済み</option>
                        <option value="closed" {{ request('status') == 'closed' ? 'selected' : '' }}>完了</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="source" class="form-label">ソース</label>
                    <select class="form-select" id="source" name="source">
                        <option value="all" {{ request('source') == 'all' ? 'selected' : '' }}>すべて</option>
                        <option value="form" {{ request('source') == 'form' ? 'selected' : '' }}>フォーム</option>
                        <option value="imap" {{ request('source') == 'imap' ? 'selected' : '' }}>メール</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">開始日</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request('date_from') }}">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">終了日</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request('date_to') }}">
                </div>
                <div class="col-12 d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary me-2">検索</button>
                    <a href="{{ route('admin.contacts.index') }}" class="btn btn-secondary">リセット</a>
                </div>
            </form>
        </div>
    </div>

    <!-- お問い合わせ一覧テーブル -->
    <div class="card">
        <div class="card-body">
            @if($contacts->isEmpty())
                <p class="text-center my-5">お問い合わせデータがありません。</p>
            @else
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>件名</th>
                                <th>名前</th>
                                <th>メールアドレス</th>
                                <th>ソース</th>
                                <th>ステータス</th>
                                <th>日付</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($contacts as $contact)
                                <tr class="{{ $contact->status === 'new' ? 'table-warning' : '' }}">
                                    <td>{{ $contact->id }}</td>
                                    <td>{{ Str::limit($contact->subject, 30) }}</td>
                                    <td>{{ $contact->name }}</td>
                                    <td>{{ $contact->email }}</td>
                                    <td>
                                        @if($contact->source === 'form')
                                            <span class="badge bg-primary">フォーム</span>
                                        @else
                                            <span class="badge bg-info">メール</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($contact->status === 'new')
                                            <span class="badge bg-warning text-dark">新規</span>
                                        @elseif($contact->status === 'replied')
                                            <span class="badge bg-success">返信済み</span>
                                        @else
                                            <span class="badge bg-secondary">完了</span>
                                        @endif
                                    </td>
                                    <td>{{ $contact->created_at->format('Y/m/d H:i') }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.contacts.show', $contact) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ $contact->id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>

                                        <!-- 削除確認モーダル -->
                                        <div class="modal fade" id="deleteModal{{ $contact->id }}" tabindex="-1" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">削除確認</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p>このお問い合わせを削除しますか？</p>
                                                        <p class="text-danger">この操作は取り消せません。</p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">キャンセル</button>
                                                        <form action="{{ route('admin.contacts.destroy', $contact) }}" method="POST">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-danger">削除する</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- ページネーション -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $contacts->appends(request()->query())->links() }}
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
