@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>お問い合わせ詳細</h1>
        <div>
            <a href="{{ route('admin.contacts.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> 一覧に戻る
            </a>
            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                <i class="fas fa-trash me-1"></i> 削除
            </button>
            
            <!-- 削除確認モーダル -->
            <div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">削除確認</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p>このお問い合わせを削除しますか？</p>
                            <p class="text-danger">この操作は取り消せません。</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">キャンセル</button>
                            <form action="{{ route('admin.contacts.destroy', $contact) }}" method="POST">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger">削除する</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- お問い合わせ情報 -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <h5 class="mb-0">{{ $contact->subject }}</h5>
                <small class="text-muted">{{ $contact->created_at->format('Y年m月d日 H:i') }}</small>
            </div>
            <div>
                @if($contact->status === 'new')
                    <span class="badge bg-warning text-dark">新規</span>
                @elseif($contact->status === 'replied')
                    <span class="badge bg-success">返信済み</span>
                @else
                    <span class="badge bg-secondary">完了</span>
                @endif
                
                @if($contact->source === 'form')
                    <span class="badge bg-primary">フォーム</span>
                @else
                    <span class="badge bg-info">メール</span>
                @endif
            </div>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <p><strong>名前:</strong> {{ $contact->name }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>メールアドレス:</strong> {{ $contact->email }}</p>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-body">
                    <div style="white-space: pre-wrap;">{{ $contact->message }}</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 返信履歴 -->
    @if($replies->isNotEmpty())
        <h4 class="mb-3">返信履歴</h4>
        <div class="card mb-4">
            <div class="card-body">
                <div class="timeline">
                    @foreach($replies as $reply)
                        <div class="timeline-item">
                            <div class="timeline-item-marker">
                                <div class="timeline-item-marker-text">{{ $reply->created_at->format('Y/m/d H:i') }}</div>
                                <div class="timeline-item-marker-indicator bg-primary"></div>
                            </div>
                            <div class="timeline-item-content">
                                <div class="card mb-3">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>管理者からの返信</strong>
                                        </div>
                                        <div>
                                            @if($reply->status === 'sent')
                                                <span class="badge bg-success">送信済み</span>
                                            @else
                                                <span class="badge bg-danger">送信失敗</span>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div style="white-space: pre-wrap;">{{ $reply->message }}</div>
                                        
                                        @if($reply->status === 'failed')
                                            <div class="alert alert-danger mt-3">
                                                <strong>エラー:</strong> {{ $reply->error_message }}
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endif
    
    <!-- 返信フォーム -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">返信する</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.contacts.reply', $contact) }}" method="POST">
                @csrf
                
                <div class="mb-3">
                    <label for="message" class="form-label">返信内容</label>
                    <textarea class="form-control @error('message') is-invalid @enderror" id="message" name="message" rows="6" required>{{ old('message') }}</textarea>
                    @error('message')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-1"></i> 返信を送信
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    /* タイムライン用スタイル */
    .timeline {
        position: relative;
        padding-left: 1.5rem;
    }
    
    .timeline:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0.75rem;
        height: 100%;
        width: 1px;
        background-color: #dee2e6;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 1.5rem;
    }
    
    .timeline-item-marker {
        position: absolute;
        left: -1.5rem;
        width: 1.5rem;
    }
    
    .timeline-item-marker-text {
        position: absolute;
        left: -5.5rem;
        width: 5rem;
        font-size: 0.75rem;
        color: #6c757d;
        text-align: right;
        white-space: nowrap;
    }
    
    .timeline-item-marker-indicator {
        position: absolute;
        top: 0.25rem;
        left: 0.25rem;
        width: 1rem;
        height: 1rem;
        border-radius: 100%;
        background-color: #fff;
        border: 2px solid #dee2e6;
    }
    
    .timeline-item-content {
        padding-left: 0.5rem;
        padding-bottom: 1.5rem;
    }
</style>
@endsection
