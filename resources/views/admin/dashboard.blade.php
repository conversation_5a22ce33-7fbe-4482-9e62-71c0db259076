@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <h1 class="mb-4">ダッシュボード</h1>
    
    <!-- 概要カード -->
    <div class="row">
        <div class="col-md-4">
            <div class="card text-white bg-primary mb-4">
                <div class="card-body">
                    <h5 class="card-title">総売上</h5>
                    <h2 class="card-text">¥{{ number_format($totalSales) }}</h2>
                    <p class="card-text">今月: ¥{{ number_format($monthlySales) }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-white bg-success mb-4">
                <div class="card-body">
                    <h5 class="card-title">受講者数</h5>
                    <h2 class="card-text">{{ number_format($totalStudents) }}人</h2>
                    <p class="card-text">今月: {{ number_format($monthlyStudents) }}人</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-white bg-info mb-4">
                <div class="card-body">
                    <h5 class="card-title">メルマガ購読者数</h5>
                    <h2 class="card-text">{{ number_format($totalSubscribers) }}人</h2>
                    <p class="card-text">今月の新規: {{ number_format($monthlySubscribers) }}人</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- グラフ -->
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    月別売上
                </div>
                <div class="card-body">
                    <canvas id="salesChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    月別受講者数
                </div>
                <div class="card-body">
                    <canvas id="studentsChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    月別メルマガ購読者増減
                </div>
                <div class="card-body">
                    <canvas id="subscribersChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    最近のお問い合わせ
                </div>
                <div class="card-body">
                    @if($recentContacts->isEmpty())
                        <p class="text-center">最近のお問い合わせはありません。</p>
                    @else
                        <div class="list-group">
                            @foreach($recentContacts as $contact)
                                <a href="{{ route('admin.contacts.show', $contact) }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h5 class="mb-1">{{ $contact->subject }}</h5>
                                        <small>{{ $contact->created_at->format('Y/m/d H:i') }}</small>
                                    </div>
                                    <p class="mb-1">{{ Str::limit($contact->message, 100) }}</p>
                                    <small>{{ $contact->name }} ({{ $contact->email }})</small>
                                    @if($contact->status === 'new')
                                        <span class="badge bg-danger float-end">新規</span>
                                    @endif
                                </a>
                            @endforeach
                        </div>
                        <div class="mt-3 text-end">
                            <a href="{{ route('admin.contacts.index') }}" class="btn btn-sm btn-outline-primary">すべて表示</a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // 月の名前
    const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
    
    // 売上グラフ
    const salesData = @json($monthlySalesData);
    const salesChartData = Array(12).fill(0);
    
    for (const [month, amount] of Object.entries(salesData)) {
        salesChartData[parseInt(month) - 1] = amount;
    }
    
    new Chart(document.getElementById('salesChart'), {
        type: 'bar',
        data: {
            labels: months,
            datasets: [{
                label: '売上 (円)',
                data: salesChartData,
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // 受講者グラフ
    const studentsData = @json($monthlyStudentsData);
    const studentsChartData = Array(12).fill(0);
    
    for (const [month, count] of Object.entries(studentsData)) {
        studentsChartData[parseInt(month) - 1] = count;
    }
    
    new Chart(document.getElementById('studentsChart'), {
        type: 'bar',
        data: {
            labels: months,
            datasets: [{
                label: '受講者数',
                data: studentsChartData,
                backgroundColor: 'rgba(75, 192, 192, 0.5)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // メルマガ購読者グラフ
    const subscribersData = @json($monthlySubscribersData);
    const subscribersChartData = Array(12).fill(0);
    
    for (const [month, count] of Object.entries(subscribersData)) {
        subscribersChartData[parseInt(month) - 1] = count;
    }
    
    new Chart(document.getElementById('subscribersChart'), {
        type: 'line',
        data: {
            labels: months,
            datasets: [{
                label: '購読者数',
                data: subscribersChartData,
                backgroundColor: 'rgba(255, 159, 64, 0.5)',
                borderColor: 'rgba(255, 159, 64, 1)',
                borderWidth: 2,
                tension: 0.1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
</script>
@endsection
