@extends('admin.layouts.app')

@section('styles')
<style>
    /* ページネーションのスタイル修正 */
    .pagination {
        display: flex;
        padding-left: 0;
        list-style: none;
        border-radius: 0.25rem;
    }
    .page-link {
        position: relative;
        display: block;
        padding: 0.5rem 0.75rem;
        margin-left: -1px;
        line-height: 1.25;
        color: #3498db;
        background-color: #fff;
        border: 1px solid #dee2e6;
    }
    .page-item.active .page-link {
        z-index: 3;
        color: #fff;
        background-color: #3498db;
        border-color: #3498db;
    }
    /* 不要なテキスト表示を削除 */
    .pagination::before,
    .pagination::after {
        display: none !important;
        content: none !important;
    }

    /* ページネーションコンテナのスタイル */
    .pagination-container {
        position: relative;
    }

    /* 「Start typing to enter text」を非表示 */
    .pagination-container::before,
    .pagination-container::after {
        display: none !important;
        content: none !important;
    }

    /* 矢印アイコンのサイズ調整 */
    .page-link {
        font-size: 14px;
    }
</style>
@endsection

@section('scripts')
<script>
    function toggleDetail(id, btnId) {
        var detailRow = document.getElementById(id);
        var btn = document.getElementById(btnId);

        if (detailRow.style.display === 'none') {
            detailRow.style.display = 'table-row';
            // ボタンのテキストを変更
            btn.innerHTML = '<i class="fas fa-minus"></i>';
        } else {
            detailRow.style.display = 'none';
            // ボタンのテキストを元に戻す
            btn.innerHTML = '<i class="fas fa-plus"></i>';
        }
    }

    // 売上データをフィルタリングする関数
    function filterSales() {
        // フィルター条件を取得
        const searchText = document.getElementById('search').value.toLowerCase();
        const status = document.getElementById('status').value;
        const dateFrom = document.getElementById('date_from').value;
        const dateTo = document.getElementById('date_to').value;

        // テーブルの行を取得
        const rows = document.querySelectorAll('table.table tbody tr:not([id^="detail-"])');

        // 表示件数カウンター
        let visibleCount = 0;

        // 各行をフィルタリング
        rows.forEach(row => {
            let showRow = true;

            // 決済ID/注文IDでフィルタリング
            if (searchText) {
                const paymentId = row.cells[0].textContent.toLowerCase();
                // 詳細行から注文IDを取得
                const detailId = row.getAttribute('data-detail-id');
                const detailRow = document.getElementById(detailId);
                let orderId = '';

                if (detailRow) {
                    // 詳細行から注文IDを検索
                    const orderIdCell = detailRow.querySelector('td[data-order-id]');
                    if (orderIdCell) {
                        orderId = orderIdCell.getAttribute('data-order-id').toLowerCase();
                    }
                }

                if (!paymentId.includes(searchText) && !orderId.includes(searchText)) {
                    showRow = false;
                }
            }

            // ステータスでフィルタリング
            if (status !== 'all' && showRow) {
                const rowStatus = row.getAttribute('data-status');
                if (rowStatus !== status) {
                    showRow = false;
                }
            }

            // 日付範囲でフィルタリング
            if ((dateFrom || dateTo) && showRow) {
                const processDate = row.getAttribute('data-date');

                if (dateFrom && processDate < dateFrom) {
                    showRow = false;
                }

                if (dateTo && processDate > dateTo) {
                    showRow = false;
                }
            }

            // 行の表示/非表示を設定
            if (showRow) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';

                // 詳細行も非表示にする
                const detailId = row.getAttribute('data-detail-id');
                if (detailId) {
                    const detailRow = document.getElementById(detailId);
                    if (detailRow) {
                        detailRow.style.display = 'none';
                    }
                }
            }
        });

        // 表示件数を更新
        document.getElementById('visible-count').textContent = visibleCount;
        document.getElementById('filtered-count').textContent = visibleCount;
    }

    // フィルターをリセットする関数
    function resetFilters() {
        document.getElementById('search').value = '';
        document.getElementById('status').value = 'all';
        document.getElementById('date_from').value = '';
        document.getElementById('date_to').value = '';

        // すべての行を表示
        const rows = document.querySelectorAll('table.table tbody tr:not([id^="detail-"])');
        rows.forEach(row => {
            row.style.display = '';
        });

        // 詳細行は非表示のままにする
        const detailRows = document.querySelectorAll('table.table tbody tr[id^="detail-"]');
        detailRows.forEach(row => {
            row.style.display = 'none';
        });

        // 表示件数を更新
        document.getElementById('visible-count').textContent = rows.length;
        document.getElementById('filtered-count').textContent = rows.length;
    }

    // ページ読み込み時にイベントリスナーを設定
    document.addEventListener('DOMContentLoaded', function() {
        // フィルター入力要素にイベントリスナーを追加
        document.getElementById('search').addEventListener('input', filterSales);
        document.getElementById('status').addEventListener('change', filterSales);
        document.getElementById('date_from').addEventListener('change', filterSales);
        document.getElementById('date_to').addEventListener('change', filterSales);

        // リセットボタンにイベントリスナーを追加
        document.getElementById('reset-filters').addEventListener('click', resetFilters);

        // 初期表示件数を設定
        const totalRows = document.querySelectorAll('table.table tbody tr:not([id^="detail-"])').length;
        document.getElementById('visible-count').textContent = totalRows;
        document.getElementById('filtered-count').textContent = totalRows;
    });
</script>
@endsection

@section('content')
<!-- 不要なテキスト表示を削除するためのインラインスタイル -->
<style>
    /* 「Start typing to enter text」を非表示 */
    body::before {
        display: none !important;
        content: none !important;
    }

    /* 矢印アイコンのサイズ調整 */
    .page-link {
        font-size: 14px !important;
    }

    /* ページネーションの矢印アイコンのサイズ調整 */
    .pagination svg {
        width: 20px !important;
        height: 20px !important;
    }
</style>

<div class="container-fluid">
    <div class="card mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="mb-0">Fincode決済データ一覧</h1>
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <h4 class="mb-0 text-success">合計: ¥{{ number_format($totalAmount) }}</h4>
                    </div>
                    <a href="{{ route('admin.sales.fincode') }}" class="btn btn-primary">
                        <i class="fas fa-sync-alt me-1"></i> 決済データを同期
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 最終同期情報 -->
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0"><i class="fas fa-sync-alt me-2"></i>同期情報</h5>
        </div>
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    @if($lastApiCall)
                        <p class="mb-1"><strong>最終同期:</strong> {{ $lastApiCall->last_called_at->format('Y年m月d日 H:i:s') }} ({{ $lastApiCall->last_called_at->diffForHumans() }})</p>
                        @if($lastApiCall->records_fetched > 0)
                            <p class="mb-1"><strong>取得件数:</strong> {{ $lastApiCall->records_fetched }}件</p>
                        @endif
                        @if($lastApiCall->status !== 'success')
                            <p class="mb-1 text-danger"><strong>ステータス:</strong> {{ $lastApiCall->status }}</p>
                        @endif
                    @else
                        <p class="mb-0">まだFincodeとの同期が行われていません。</p>
                    @endif
                </div>
                <div>
                    @if($lastApiCall && $lastApiCall->last_called_at->diffInMinutes(now()) < 60)
                        <div class="alert alert-success mb-0">
                            <i class="fas fa-clock me-1"></i> 自動同期: 次回は {{ $lastApiCall->last_called_at->addHour()->format('H:i') }} 以降
                        </div>
                    @else
                        <div class="alert alert-warning mb-0">
                            <i class="fas fa-exclamation-triangle me-1"></i> 自動同期: 次回ページ読み込み時
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- 検索・フィルター -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-filter me-2"></i>検索・フィルター</h5>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">検索</label>
                    <input type="text" class="form-control" id="search" placeholder="決済ID、注文ID">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">ステータス</label>
                    <select class="form-select" id="status">
                        <option value="all">すべて</option>
                        <option value="UNPROCESSED">未処理</option>
                        <option value="AUTHORIZED">仮売上</option>
                        <option value="CAPTURED">売上確定</option>
                        <option value="CANCELED">キャンセル</option>
                        <option value="REFUNDED">返金済み</option>
                        <option value="FAILED">失敗</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="date_from" class="form-label">開始日</label>
                    <input type="date" class="form-control" id="date_from">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">終了日</label>
                    <input type="date" class="form-control" id="date_to">
                </div>
                <div class="col-md-12 d-flex justify-content-between mt-4">
                    <div>
                        <span class="badge bg-info">
                            <i class="fas fa-info-circle me-1"></i> 表示中: <span id="visible-count">0</span> / <span id="filtered-count">0</span> 件
                        </span>
                    </div>
                    <div>
                        <button type="button" id="reset-filters" class="btn btn-secondary">
                            <i class="fas fa-redo me-1"></i> フィルターをリセット
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 売上一覧テーブル -->
    <div class="card">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0"><i class="fas fa-list me-2"></i>決済データ一覧</h5>
        </div>
        <div class="card-body">
            @if($sales->isEmpty())
                <p class="text-center my-5">売上データがありません。</p>
            @else
                <div class="table-responsive">
                    <table class="table table-hover table-bordered">
                        <thead class="table-primary">
                            <tr>
                                <th>決済ID</th>
                                <th>金額</th>
                                <th>ステータス</th>
                                <th>請求タイプ</th>
                                <th>カード番号</th>
                                <th>カードブランド</th>
                                <th>処理日時</th>
                                <th>詳細</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($sales as $index => $sale)
                                @php
                                    // ステータスに基づく行の色分け用クラス
                                    $rowClass = '';
                                    switch($sale->status) {
                                        case 'CAPTURED': $rowClass = 'table-success'; break;
                                        case 'AUTHORIZED': $rowClass = 'table-warning'; break;
                                        case 'UNPROCESSED': $rowClass = 'table-light'; break;
                                        case 'FAILED': $rowClass = 'table-danger'; break;
                                        default: $rowClass = '';
                                    }
                                @endphp
                                <tr class="{{ $rowClass }}"
                                    data-status="{{ $sale->status }}"
                                    data-date="{{ $sale->process_date ? $sale->process_date->format('Y-m-d') : '' }}"
                                    data-detail-id="detail-{{ $index }}">
                                    <td>{{ $sale->payment_id }}</td>
                                    <td class="text-end fw-bold">{{ number_format($sale->amount) }}円</td>
                                    <td>
                                        <span class="badge bg-{{ $sale->status === 'CAPTURED' ? 'success' : ($sale->status === 'AUTHORIZED' ? 'warning' : ($sale->status === 'CANCELED' ? 'danger' : ($sale->status === 'REFUNDED' ? 'info' : 'secondary'))) }}">
                                            {{ $sale->status_text }}
                                        </span>
                                    </td>
                                    <td>{{ $sale->job_code_text }}</td>
                                    <td>{{ $sale->card_no ?? '未設定' }}</td>
                                    <td>{{ $sale->brand ?? '未設定' }}</td>
                                    <td>{{ $sale->process_date ? $sale->process_date->format('Y/m/d H:i:s') : '未設定' }}</td>
                                    <td>
                                        <button id="btn-{{ $index }}" class="btn btn-sm btn-primary rounded-circle" onclick="toggleDetail('detail-{{ $index }}', 'btn-{{ $index }}')">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr id="detail-{{ $index }}" style="display: none;">
                                    <td colspan="8" class="p-0">
                                        <div class="p-3 bg-light">
                                            <h5 class="border-bottom pb-2 mb-3">決済詳細</h5>
                                            <div class="table-responsive">
                                                <table class="table table-bordered">
                                                    <tr>
                                                        <th class="table-light" style="width: 150px;">決済ID</th>
                                                        <td>{{ $sale->payment_id }}</td>
                                                        <th class="table-light" style="width: 150px;">ショップID</th>
                                                        <td>{{ $sale->shop_id ?? '未設定' }}</td>
                                                    </tr>
                                                    <tr>
                                                        <th class="table-light">注文ID</th>
                                                        <td data-order-id="{{ $sale->order_id ?? '' }}">{{ $sale->order_id ?? '未設定' }}</td>
                                                        <th class="table-light">決済タイプ</th>
                                                        <td>{{ $sale->pay_type ?? '未設定' }}</td>
                                                    </tr>
                                                    <tr>
                                                        <th class="table-light">アクセスID</th>
                                                        <td>{{ $sale->access_id ?? '未設定' }}</td>
                                                        <th class="table-light">トランザクションID</th>
                                                        <td>{{ $sale->transaction_id ?? '未設定' }}</td>
                                                    </tr>
                                                    <tr>
                                                        <th class="table-light">カード名義</th>
                                                        <td>{{ $sale->holder_name ?? '未設定' }}</td>
                                                        <th class="table-light">有効期限</th>
                                                        <td>{{ $sale->expire ?? '未設定' }}</td>
                                                    </tr>
                                                    <tr>
                                                        <th class="table-light">支払い方法</th>
                                                        <td>{{ $sale->method_text }}</td>
                                                        <th class="table-light">分割回数</th>
                                                        <td>{{ $sale->pay_times ?? '-' }}</td>
                                                    </tr>
                                                    @if(!empty($sale->client_field_1))
                                                    <tr>
                                                        <th class="table-light">加盟店自由項目1</th>
                                                        <td colspan="3">{{ $sale->client_field_1 }}</td>
                                                    </tr>
                                                    @endif
                                                    @if(!empty($sale->client_field_2))
                                                    <tr>
                                                        <th class="table-light">加盟店自由項目2</th>
                                                        <td colspan="3">{{ $sale->client_field_2 }}</td>
                                                    </tr>
                                                    @endif
                                                    @if(!empty($sale->client_field_3))
                                                    <tr>
                                                        <th class="table-light">加盟店自由項目3</th>
                                                        <td colspan="3">{{ $sale->client_field_3 }}</td>
                                                    </tr>
                                                    @endif
                                                    <tr>
                                                        <th class="table-light">作成日時</th>
                                                        <td>{{ $sale->created ? $sale->created->format('Y/m/d H:i:s') : '未設定' }}</td>
                                                        <th class="table-light">更新日時</th>
                                                        <td>{{ $sale->updated ? $sale->updated->format('Y/m/d H:i:s') : '未設定' }}</td>
                                                    </tr>
                                                    @if($sale->student)
                                                    <tr>
                                                        <th class="table-light">学生情報</th>
                                                        <td colspan="3">
                                                            <strong>名前:</strong> {{ $sale->student->name ?? '不明' }}<br>
                                                            <strong>メール:</strong> {{ $sale->student->email ?? '不明' }}<br>
                                                            <strong>コース:</strong> {{ $sale->student->course->name ?? '不明' }}
                                                        </td>
                                                    </tr>
                                                    @endif
                                                    <tr>
                                                        <th class="table-light">操作</th>
                                                        <td colspan="3">
                                                            @if($sale->student)
                                                                <a href="{{ route('admin.sales.show', $sale->student) }}" class="btn btn-sm btn-info" title="詳細を表示">
                                                                    <i class="fas fa-eye"></i> 学生詳細
                                                                </a>
                                                            @else
                                                                <span class="badge bg-secondary">学生情報なし</span>
                                                            @endif

                                                            @if($sale->status === 'CAPTURED')
                                                                <button type="button" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#refundModal{{ $sale->id }}" title="返金処理">
                                                                    <i class="fas fa-undo"></i> 返金処理
                                                                </button>

                                                                <!-- 返金確認モーダル -->
                                                                <div class="modal fade" id="refundModal{{ $sale->id }}" tabindex="-1" aria-hidden="true">
                                                                    <div class="modal-dialog">
                                                                        <div class="modal-content">
                                                                            <div class="modal-header">
                                                                                <h5 class="modal-title">返金確認</h5>
                                                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                            </div>
                                                                            <div class="modal-body">
                                                                                <p>決済ID: {{ $sale->payment_id }}</p>
                                                                                <p>金額: ¥{{ number_format($sale->amount) }}</p>
                                                                                <p>この決済を返金しますか？</p>
                                                                                <p class="text-danger">この操作は取り消せません。</p>
                                                                            </div>
                                                                            <div class="modal-footer">
                                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">キャンセル</button>
                                                                                <form action="{{ route('admin.sales.refund', $sale->payment_id) }}" method="POST">
                                                                                    @csrf
                                                                                    <button type="submit" class="btn btn-danger">返金する</button>
                                                                                </form>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            @endif
                                                        </td>
                                                    </tr>
                                                    @if($sale->status === 'CAPTURED')
                                                    <tr>
                                                        <th class="table-light">返金</th>
                                                        <td colspan="3">
                                                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#refundModalBottom{{ $sale->id }}">
                                                                <i class="fas fa-undo me-1"></i> 返金
                                                            </button>

                                                            <!-- 返金確認モーダル（下部ボタン用） -->
                                                            <div class="modal fade" id="refundModalBottom{{ $sale->id }}" tabindex="-1" aria-hidden="true">
                                                                <div class="modal-dialog">
                                                                    <div class="modal-content">
                                                                        <div class="modal-header">
                                                                            <h5 class="modal-title">返金確認</h5>
                                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                        </div>
                                                                        <div class="modal-body">
                                                                            <p>決済ID: {{ $sale->payment_id }}</p>
                                                                            <p>金額: ¥{{ number_format($sale->amount) }}</p>
                                                                            <p>この決済を返金しますか？</p>
                                                                            <p class="text-danger">この操作は取り消せません。</p>
                                                                        </div>
                                                                        <div class="modal-footer">
                                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">キャンセル</button>
                                                                            <form action="{{ route('admin.sales.refund', $sale->payment_id) }}" method="POST">
                                                                                @csrf
                                                                                <button type="submit" class="btn btn-danger">返金する</button>
                                                                            </form>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    @endif
                                                </table>
                                            </div>

                                            <div class="mt-4 pt-3 border-top">
                                                <h5>レスポンスデータ</h5>
                                                <pre class="bg-light p-3 border rounded" style="max-height: 300px; overflow: auto;">{{ json_encode($sale->payment_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- 件数表示 -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        <p class="mb-0">フィルタリング機能はクライアントサイドで動作しています</p>
                    </div>
                    <div>
                        <p class="mb-0"><strong>総件数:</strong> {{ $sales->count() }} 件</p>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
