@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>売上詳細</h1>
        <div>
            <a href="{{ route('admin.sales.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> 一覧に戻る
            </a>
            @if($student->payment_status === 'completed')
                <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#refundModal">
                    <i class="fas fa-undo me-1"></i> 返金する
                </button>

                <!-- 返金確認モーダル -->
                <div class="modal fade" id="refundModal" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">返金確認</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <p>{{ $student->name }}様の決済(¥{{ number_format($student->amount) }})を返金しますか？</p>
                                <p class="text-danger">この操作は取り消せません。</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">キャンセル</button>
                                <form action="{{ route('admin.sales.destroy', $student) }}" method="POST">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger">返金する</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <div class="row">
        <!-- 購入者情報 -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    購入者情報
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th style="width: 30%">ID</th>
                            <td>{{ $student->id }}</td>
                        </tr>
                        <tr>
                            <th>名前</th>
                            <td>{{ $student->name }}</td>
                        </tr>
                        <tr>
                            <th>メールアドレス</th>
                            <td>{{ $student->email }}</td>
                        </tr>
                        <tr>
                            <th>電話番号</th>
                            <td>{{ $student->phone ?? '未設定' }}</td>
                        </tr>
                        <tr>
                            <th>住所</th>
                            <td>{{ $student->address ?? '未設定' }}</td>
                        </tr>
                        <tr>
                            <th>登録日</th>
                            <td>{{ $student->created_at->format('Y年m月d日 H:i') }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- 注文情報 -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    注文情報
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th style="width: 30%">注文ID</th>
                            <td>{{ $student->order_id }}</td>
                        </tr>
                        <tr>
                            <th>コース</th>
                            <td>{{ $student->course->name ?? '不明' }}</td>
                        </tr>
                        <tr>
                            <th>金額</th>
                            <td>¥{{ number_format($student->amount) }}</td>
                        </tr>
                        <tr>
                            <th>決済ステータス</th>
                            <td>
                                @if($student->payment_status === 'completed')
                                    <span class="badge bg-success">売上確定</span>
                                @elseif($student->payment_status === 'pending')
                                    <span class="badge bg-warning text-dark">保留中</span>
                                @elseif($student->payment_status === 'cancelled')
                                    <span class="badge bg-danger">キャンセル</span>
                                @elseif($student->payment_status === 'refunded')
                                    <span class="badge bg-info">返金済み</span>
                                @else
                                    <span class="badge bg-secondary">{{ $student->payment_status }}</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>決済タイプ</th>
                            <td>
                                @if($student->payment_type === 'Card')
                                    <span class="badge bg-primary">クレジットカード</span>
                                @elseif($student->payment_type === 'Paypay')
                                    <span class="badge bg-success">PayPay</span>
                                @elseif($student->payment_type === 'konbini')
                                    <span class="badge bg-warning text-dark">コンビニ決済</span>
                                @elseif($student->payment_type === 'Banktransfer')
                                    <span class="badge bg-secondary">銀行振込</span>
                                @elseif($student->payment_type === 'Carrier')
                                    <span class="badge bg-info">キャリア決済</span>
                                @else
                                    <span class="badge bg-secondary">{{ $student->payment_type ?? '不明' }}</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>決済ID</th>
                            <td>{{ $student->payment_id ?? '未設定' }}</td>
                        </tr>
                        <tr>
                            <th>決済日時</th>
                            <td>{{ $student->updated_at->format('Y年m月d日 H:i') }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 決済詳細情報 -->
    @if($paymentDetails)
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Fincode決済詳細</h5>
                <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#collapsePaymentDetails" aria-expanded="true">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </div>
            <div class="collapse show" id="collapsePaymentDetails">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <tr>
                                <th style="width: 20%">決済ID</th>
                                <td>{{ $paymentDetails['id'] ?? '不明' }}</td>
                                <th style="width: 20%">取引ID</th>
                                <td>{{ $paymentDetails['access_id'] ?? '不明' }}</td>
                            </tr>
                            <tr>
                                <th>カード情報</th>
                                <td>
                                    @if(isset($paymentDetails['card']))
                                        {{ $paymentDetails['card']['brand'] ?? '' }} **** **** **** {{ $paymentDetails['card']['last4'] ?? '' }}
                                        (有効期限: {{ $paymentDetails['card']['expire'] ?? '' }})
                                    @else
                                        情報なし
                                    @endif
                                </td>
                                <th>カード名義</th>
                                <td>{{ $paymentDetails['holder_name'] ?? '不明' }}</td>
                            </tr>
                            <tr>
                                <th>取引ステータス</th>
                                <td>
                                    @php
                                        $statusText = '';
                                        switch($paymentDetails['status'] ?? '') {
                                            case 'UNPROCESSED': $statusText = '未処理'; break;
                                            case 'CHECKED': $statusText = '有効性チェック済み'; break;
                                            case 'AUTHORIZED': $statusText = '仮売上'; break;
                                            case 'AWAITING_CUSTOMER_PAYMENT': $statusText = '顧客支払い待ち'; break;
                                            case 'AWAITING_PAYMENT_APPROVAL': $statusText = '支払い承認待ち'; break;
                                            case 'CAPTURED': $statusText = '売上確定'; break;
                                            case 'CANCELED': $statusText = 'キャンセル'; break;
                                            case 'EXPIRED': $statusText = '期限切れ'; break;
                                            case 'AUTHENTICATED': $statusText = '未処理（3Dセキュア認証待ち）'; break;
                                            case 'FAILED': $statusText = '失敗'; break;
                                            default: $statusText = $paymentDetails['status'] ?? '未設定';
                                        }
                                    @endphp
                                    <span class="badge bg-primary">{{ $paymentDetails['status'] ?? '不明' }}</span>
                                    {{ $statusText }}
                                </td>
                                <th>請求タイプ</th>
                                <td>
                                    @php
                                        $jobCodeText = '';
                                        switch($paymentDetails['job_code'] ?? '') {
                                            case 'AUTH': $jobCodeText = '仮売上'; break;
                                            case 'CAPTURE': $jobCodeText = '売上確定'; break;
                                            case 'SALES': $jobCodeText = '売上'; break;
                                            default: $jobCodeText = $paymentDetails['job_code'] ?? '未設定';
                                        }
                                    @endphp
                                    {{ $jobCodeText }}
                                </td>
                            </tr>
                            <tr>
                                <th>承認コード</th>
                                <td>{{ $paymentDetails['approve_code'] ?? '不明' }}</td>
                                <th>取引日時</th>
                                <td>{{ $paymentDetails['transaction_time'] ?? '不明' }}</td>
                            </tr>
                            @if(!empty($paymentDetails['client_field_1']) || !empty($paymentDetails['client_field_2']) || !empty($paymentDetails['client_field_3']))
                            <tr>
                                <th>加盟店自由項目1</th>
                                <td>{{ $paymentDetails['client_field_1'] ?? '' }}</td>
                                <th>加盟店自由項目2</th>
                                <td>{{ $paymentDetails['client_field_2'] ?? '' }}</td>
                            </tr>
                            <tr>
                                <th>加盟店自由項目3</th>
                                <td colspan="3">{{ $paymentDetails['client_field_3'] ?? '' }}</td>
                            </tr>
                            @endif
                        </table>
                    </div>

                    <div class="mt-4">
                        <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#collapseRawJson" aria-expanded="false">
                            <i class="fas fa-code me-1"></i> JSON形式で表示
                        </button>
                        <div class="collapse mt-2" id="collapseRawJson">
                            <div class="card card-body bg-light">
                                <pre class="mb-0" style="max-height: 300px; overflow-y: auto;">{{ json_encode($paymentDetails, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- 受講者管理へのリンク -->
    <div class="mt-4">
        <a href="{{ route('admin.students.show', $student) }}" class="btn btn-primary">
            <i class="fas fa-user-graduate me-1"></i> 受講者詳細を表示
        </a>
    </div>
</div>
@endsection
