@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Fincode決済データ一覧</h1>
        <div>
            <a href="{{ route('admin.sales.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> 売上一覧に戻る
            </a>
        </div>
    </div>

    <!-- 検索・フィルター -->
    <div class="card mb-4">
        <div class="card-body">
            <form action="{{ route('admin.sales.fincode') }}" method="GET" class="row g-3">
                <div class="col-md-2">
                    <label for="order_id" class="form-label">注文ID</label>
                    <input type="text" class="form-control" id="order_id" name="order_id" value="{{ request('order_id') }}" placeholder="注文ID">
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">ステータス</label>
                    <select class="form-select" id="status" name="status">
                        <option value="all" {{ request('status') == 'all' ? 'selected' : '' }}>すべて</option>
                        <option value="UNPROCESSED" {{ request('status') == 'UNPROCESSED' ? 'selected' : '' }}>未処理</option>
                        <option value="AUTHORIZED" {{ request('status') == 'AUTHORIZED' ? 'selected' : '' }}>仮売上</option>
                        <option value="CAPTURED" {{ request('status') == 'CAPTURED' ? 'selected' : '' }}>売上確定</option>
                        <option value="CANCELED" {{ request('status') == 'CANCELED' ? 'selected' : '' }}>キャンセル</option>
                        <option value="EXPIRED" {{ request('status') == 'EXPIRED' ? 'selected' : '' }}>期限切れ</option>
                        <option value="FAILED" {{ request('status') == 'FAILED' ? 'selected' : '' }}>失敗</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="pay_type" class="form-label">決済タイプ</label>
                    <select class="form-select" id="pay_type" name="pay_type">
                        <option value="Card" {{ request('pay_type') == 'Card' ? 'selected' : '' }}>クレジットカード</option>
                        <option value="Paypay" {{ request('pay_type') == 'Paypay' ? 'selected' : '' }}>PayPay</option>
                        <option value="konbini" {{ request('pay_type') == 'konbini' ? 'selected' : '' }}>コンビニ決済</option>
                        <option value="Banktransfer" {{ request('pay_type') == 'Banktransfer' ? 'selected' : '' }}>銀行振込</option>
                        <option value="Carrier" {{ request('pay_type') == 'Carrier' ? 'selected' : '' }}>キャリア決済</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">開始日</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request('date_from') }}">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">終了日</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request('date_to') }}">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">検索</button>
                    <a href="{{ route('admin.sales.fincode') }}" class="btn btn-secondary">リセット</a>
                </div>
            </form>
        </div>
    </div>

    <!-- リクエスト情報 -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">リクエスト情報</h5>
            <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#collapseRequestInfo" aria-expanded="false">
                <i class="fas fa-chevron-down"></i>
            </button>
        </div>
        <div class="collapse" id="collapseRequestInfo">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>APIパラメータ</h6>
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>パラメータ名</th>
                                    <th>値</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($params as $key => $value)
                                <tr>
                                    <td>{{ $key }}</td>
                                    <td>{{ $value }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>APIエンドポイント</h6>
                        <code>{{ env('FINCODE_ENVIRONMENT') === 'production' ? 'https://api.fincode.jp' : 'https://api.test.fincode.jp' }}/v1/payments?{{ http_build_query($params) }}</code>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 決済データ一覧 -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">決済データ一覧</h5>
            @if(isset($paymentData['total_count']))
                <span class="badge bg-info">総件数: {{ $paymentData['total_count'] }} 件</span>
            @endif
        </div>
        <div class="card-body">
            @if(isset($paymentData['list']) && count($paymentData['list']) > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-primary">
                            <tr>
                                <th>決済ID</th>
                                <th>注文ID</th>
                                <th>金額</th>
                                <th>ステータス</th>
                                <th>請求タイプ</th>
                                <th>カード番号</th>
                                <th>カードブランド</th>
                                <th>処理日時</th>
                                <th>詳細</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($paymentData['list'] as $index => $payment)
                                @php
                                    // ステータスの日本語表示
                                    $statusText = '';
                                    switch($payment['status'] ?? '') {
                                        case 'UNPROCESSED': $statusText = '未処理'; break;
                                        case 'CHECKED': $statusText = '有効性チェック済み'; break;
                                        case 'AUTHORIZED': $statusText = '仮売上'; break;
                                        case 'AWAITING_CUSTOMER_PAYMENT': $statusText = '顧客支払い待ち'; break;
                                        case 'AWAITING_PAYMENT_APPROVAL': $statusText = '支払い承認待ち'; break;
                                        case 'CAPTURED': $statusText = '売上確定'; break;
                                        case 'CANCELED': $statusText = 'キャンセル'; break;
                                        case 'EXPIRED': $statusText = '期限切れ'; break;
                                        case 'AUTHENTICATED': $statusText = '未処理（3Dセキュア認証待ち）'; break;
                                        case 'FAILED': $statusText = '失敗'; break;
                                        default: $statusText = $payment['status'] ?? '未設定';
                                    }

                                    // 請求タイプの日本語表示
                                    $jobCodeText = '';
                                    switch($payment['job_code'] ?? '') {
                                        case 'AUTH': $jobCodeText = '仮売上'; break;
                                        case 'CAPTURE': $jobCodeText = '売上確定'; break;
                                        case 'SALES': $jobCodeText = '売上'; break;
                                        default: $jobCodeText = $payment['job_code'] ?? '未設定';
                                    }

                                    // ステータスに基づく行の色分け用クラス
                                    $rowClass = '';
                                    switch($payment['status'] ?? '') {
                                        case 'CAPTURED': $rowClass = 'table-success'; break;
                                        case 'AUTHORIZED': $rowClass = 'table-info'; break;
                                        case 'UNPROCESSED': $rowClass = 'table-warning'; break;
                                        case 'FAILED': $rowClass = 'table-danger'; break;
                                        case 'CANCELED': $rowClass = 'table-danger'; break;
                                        default: $rowClass = '';
                                    }
                                @endphp
                                <tr class="{{ $rowClass }}">
                                    <td>{{ $payment['id'] ?? '未設定' }}</td>
                                    <td>{{ $payment['order_id'] ?? '未設定' }}</td>
                                    <td class="text-end fw-bold">¥{{ number_format((int)($payment['amount'] ?? 0)) }}</td>
                                    <td>
                                        <span class="badge bg-primary">{{ $payment['status'] ?? '不明' }}</span>
                                        {{ $statusText }}
                                    </td>
                                    <td>{{ $jobCodeText }}</td>
                                    <td>{{ $payment['card_no'] ?? '未設定' }}</td>
                                    <td>{{ $payment['brand'] ?? '未設定' }}</td>
                                    <td>{{ $payment['process_date'] ?? '未設定' }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-info" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ $index }}" aria-expanded="false">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="9" class="p-0">
                                        <div class="collapse" id="collapse{{ $index }}">
                                            <div class="card card-body m-2">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <h6>決済詳細</h6>
                                                        <table class="table table-sm">
                                                            <tr>
                                                                <th>決済ID</th>
                                                                <td>{{ $payment['id'] ?? '未設定' }}</td>
                                                                <th>ショップID</th>
                                                                <td>{{ $payment['shop_id'] ?? '未設定' }}</td>
                                                            </tr>
                                                            <tr>
                                                                <th>アクセスID</th>
                                                                <td>{{ $payment['access_id'] ?? '未設定' }}</td>
                                                                <th>トランザクションID</th>
                                                                <td>{{ $payment['transaction_id'] ?? '未設定' }}</td>
                                                            </tr>
                                                            <tr>
                                                                <th>カード名義</th>
                                                                <td>{{ $payment['holder_name'] ?? '未設定' }}</td>
                                                                <th>有効期限</th>
                                                                <td>{{ $payment['expire'] ?? '未設定' }}</td>
                                                            </tr>
                                                            <tr>
                                                                <th>支払い方法</th>
                                                                <td>
                                                                    @php
                                                                        $methodText = '';
                                                                        switch($payment['method'] ?? '') {
                                                                            case '1': $methodText = '一括払い'; break;
                                                                            case '2': $methodText = '分割払い'; break;
                                                                            default: $methodText = $payment['method'] ?? '未設定';
                                                                        }
                                                                    @endphp
                                                                    {{ $methodText }}
                                                                </td>
                                                                <th>分割回数</th>
                                                                <td>{{ $payment['pay_times'] ?? '-' }}</td>
                                                            </tr>
                                                            @if(!empty($payment['client_field_1']))
                                                            <tr>
                                                                <th>加盟店自由項目1</th>
                                                                <td colspan="3">{{ $payment['client_field_1'] }}</td>
                                                            </tr>
                                                            @endif
                                                            @if(!empty($payment['client_field_2']))
                                                            <tr>
                                                                <th>加盟店自由項目2</th>
                                                                <td colspan="3">{{ $payment['client_field_2'] }}</td>
                                                            </tr>
                                                            @endif
                                                            @if(!empty($payment['client_field_3']))
                                                            <tr>
                                                                <th>加盟店自由項目3</th>
                                                                <td colspan="3">{{ $payment['client_field_3'] }}</td>
                                                            </tr>
                                                            @endif
                                                            <tr>
                                                                <th>作成日時</th>
                                                                <td>{{ $payment['created'] ?? '未設定' }}</td>
                                                                <th>更新日時</th>
                                                                <td>{{ $payment['updated'] ?? '未設定' }}</td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <h6>JSONデータ</h6>
                                                        <pre class="mb-0" style="max-height: 300px; overflow-y: auto;">{{ json_encode($payment, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre>
                                                    </div>
                                                </div>
                                                
                                                <!-- 対応する受講者データを検索するボタン -->
                                                <div class="mt-3">
                                                    <a href="{{ route('admin.sales.index', ['search' => $payment['order_id'] ?? '']) }}" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-search me-1"></i> この注文IDで受講者データを検索
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <p class="text-center my-5">決済データが見つかりませんでした。</p>
            @endif
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // ページ読み込み時に特定のオーダーIDがあればその行を強調表示
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const orderId = urlParams.get('order_id');
        
        if (orderId) {
            const rows = document.querySelectorAll('tr');
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length > 1 && cells[1].textContent.trim() === orderId) {
                    row.classList.add('table-warning');
                    row.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    
                    // 強調表示のアニメーション
                    setTimeout(function() {
                        row.style.transition = 'background-color 1s';
                        row.style.backgroundColor = '#ffeb3b';
                        
                        setTimeout(function() {
                            row.style.backgroundColor = '';
                        }, 1000);
                    }, 500);
                }
            });
        }
    });
</script>
@endsection
