@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <h1 class="mb-4">受講者一覧</h1>
    
    <!-- 検索・フィルター -->
    <div class="card mb-4">
        <div class="card-body">
            <form action="{{ route('admin.students.index') }}" method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">検索</label>
                    <input type="text" class="form-control" id="search" name="search" value="{{ request('search') }}" placeholder="名前、メールアドレス">
                </div>
                <div class="col-md-3">
                    <label for="course_id" class="form-label">コース</label>
                    <select class="form-select" id="course_id" name="course_id">
                        <option value="all" {{ request('course_id') == 'all' ? 'selected' : '' }}>すべてのコース</option>
                        @foreach($courses as $course)
                            <option value="{{ $course->id }}" {{ request('course_id') == $course->id ? 'selected' : '' }}>
                                {{ $course->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">ステータス</label>
                    <select class="form-select" id="status" name="status">
                        <option value="all" {{ request('status') == 'all' ? 'selected' : '' }}>すべて</option>
                        <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>受講中</option>
                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>保留中</option>
                        <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>キャンセル</option>
                        <option value="refunded" {{ request('status') == 'refunded' ? 'selected' : '' }}>返金済み</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">検索</button>
                    <a href="{{ route('admin.students.index') }}" class="btn btn-secondary">リセット</a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- 受講者一覧テーブル -->
    <div class="card">
        <div class="card-body">
            @if($students->isEmpty())
                <p class="text-center my-5">受講者データがありません。</p>
            @else
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>名前</th>
                                <th>メールアドレス</th>
                                <th>コース</th>
                                <th>加入日</th>
                                <th>ステータス</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($students as $student)
                                <tr>
                                    <td>{{ $student->id }}</td>
                                    <td>{{ $student->name }}</td>
                                    <td>{{ $student->email }}</td>
                                    <td>{{ $student->course->name ?? '不明' }}</td>
                                    <td>{{ $student->created_at->format('Y/m/d') }}</td>
                                    <td>
                                        @if($student->payment_status === 'completed')
                                            <span class="badge bg-success">受講中</span>
                                        @elseif($student->payment_status === 'pending')
                                            <span class="badge bg-warning text-dark">保留中</span>
                                        @elseif($student->payment_status === 'cancelled')
                                            <span class="badge bg-danger">キャンセル</span>
                                        @elseif($student->payment_status === 'refunded')
                                            <span class="badge bg-info">返金済み</span>
                                        @else
                                            <span class="badge bg-secondary">{{ $student->payment_status }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.students.show', $student) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.students.edit', $student) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ $student->id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                        
                                        <!-- 削除確認モーダル -->
                                        <div class="modal fade" id="deleteModal{{ $student->id }}" tabindex="-1" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">削除確認</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p>{{ $student->name }}さんの受講者データを削除しますか？</p>
                                                        <p class="text-danger">この操作は取り消せません。</p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">キャンセル</button>
                                                        <form action="{{ route('admin.students.destroy', $student) }}" method="POST">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-danger">削除する</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- ページネーション -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $students->appends(request()->query())->links() }}
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
