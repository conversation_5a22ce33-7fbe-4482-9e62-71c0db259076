@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>受講者詳細</h1>
        <div>
            <a href="{{ route('admin.students.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> 一覧に戻る
            </a>
            <a href="{{ route('admin.students.edit', $student) }}" class="btn btn-primary">
                <i class="fas fa-edit me-1"></i> 編集
            </a>
            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                <i class="fas fa-trash me-1"></i> 削除
            </button>
            
            <!-- 削除確認モーダル -->
            <div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">削除確認</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p>{{ $student->name }}さんの受講者データを削除しますか？</p>
                            <p class="text-danger">この操作は取り消せません。</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">キャンセル</button>
                            <form action="{{ route('admin.students.destroy', $student) }}" method="POST">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger">削除する</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- 受講者情報 -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    受講者情報
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th style="width: 30%">ID</th>
                            <td>{{ $student->id }}</td>
                        </tr>
                        <tr>
                            <th>名前</th>
                            <td>{{ $student->name }}</td>
                        </tr>
                        <tr>
                            <th>メールアドレス</th>
                            <td>{{ $student->email }}</td>
                        </tr>
                        <tr>
                            <th>電話番号</th>
                            <td>{{ $student->phone ?? '未設定' }}</td>
                        </tr>
                        <tr>
                            <th>住所</th>
                            <td>{{ $student->address ?? '未設定' }}</td>
                        </tr>
                        <tr>
                            <th>登録日</th>
                            <td>{{ $student->created_at->format('Y年m月d日 H:i') }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- コース情報 -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    コース情報
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th style="width: 30%">コース名</th>
                            <td>{{ $student->course->name ?? '不明' }}</td>
                        </tr>
                        <tr>
                            <th>コース説明</th>
                            <td>{{ $student->course->description ?? '不明' }}</td>
                        </tr>
                        <tr>
                            <th>レベル</th>
                            <td>{{ $student->course->level ?? '不明' }}</td>
                        </tr>
                        <tr>
                            <th>金額</th>
                            <td>¥{{ number_format($student->amount) }}</td>
                        </tr>
                        <tr>
                            <th>決済ステータス</th>
                            <td>
                                @if($student->payment_status === 'completed')
                                    <span class="badge bg-success">受講中</span>
                                @elseif($student->payment_status === 'pending')
                                    <span class="badge bg-warning text-dark">保留中</span>
                                @elseif($student->payment_status === 'cancelled')
                                    <span class="badge bg-danger">キャンセル</span>
                                @elseif($student->payment_status === 'refunded')
                                    <span class="badge bg-info">返金済み</span>
                                @else
                                    <span class="badge bg-secondary">{{ $student->payment_status }}</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>注文ID</th>
                            <td>{{ $student->order_id }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 売上詳細へのリンク -->
    <div class="mt-4">
        <a href="{{ route('admin.sales.show', $student) }}" class="btn btn-info">
            <i class="fas fa-yen-sign me-1"></i> 売上詳細を表示
        </a>
    </div>
</div>
@endsection
