@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <h1 class="mb-4">メルマガ送信ログ</h1>
    
    <div class="card">
        <div class="card-body">
            @if($newsletters->isEmpty())
                <p class="text-center my-5">送信済みのメルマガはありません。</p>
            @else
                <div class="accordion" id="newsletterAccordion">
                    @foreach($newsletters as $newsletter)
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading{{ $newsletter->id }}">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ $newsletter->id }}" aria-expanded="false" aria-controls="collapse{{ $newsletter->id }}">
                                    <div class="d-flex justify-content-between align-items-center w-100 me-3">
                                        <div>
                                            <strong>{{ $newsletter->subject }}</strong>
                                        </div>
                                        <div class="text-muted">
                                            送信日: {{ $newsletter->sent_at->format('Y/m/d H:i') }} | 
                                            送信数: {{ $newsletter->recipients_count }}件
                                        </div>
                                    </div>
                                </button>
                            </h2>
                            <div id="collapse{{ $newsletter->id }}" class="accordion-collapse collapse" aria-labelledby="heading{{ $newsletter->id }}" data-bs-parent="#newsletterAccordion">
                                <div class="accordion-body">
                                    <div class="mb-4">
                                        <h5>メール内容</h5>
                                        <div class="card">
                                            <div class="card-body">
                                                <h6>件名: {{ $newsletter->subject }}</h6>
                                                <hr>
                                                <div style="white-space: pre-wrap;">{{ $newsletter->content }}</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <h5>送信ログ</h5>
                                    @if($newsletter->logs->isEmpty())
                                        <p>詳細なログがありません。</p>
                                    @else
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>メールアドレス</th>
                                                        <th>ステータス</th>
                                                        <th>送信日時</th>
                                                        <th>エラーメッセージ</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($newsletter->logs as $log)
                                                        <tr>
                                                            <td>{{ $log->recipient_email }}</td>
                                                            <td>
                                                                @if($log->status === 'sent')
                                                                    <span class="badge bg-success">送信済み</span>
                                                                @elseif($log->status === 'failed')
                                                                    <span class="badge bg-danger">失敗</span>
                                                                @elseif($log->status === 'opened')
                                                                    <span class="badge bg-info">開封済み</span>
                                                                @endif
                                                            </td>
                                                            <td>{{ $log->created_at->format('Y/m/d H:i:s') }}</td>
                                                            <td>{{ $log->error_message }}</td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
                
                <!-- ページネーション -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $newsletters->links() }}
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
