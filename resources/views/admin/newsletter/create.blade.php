@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <h1 class="mb-4">メルマガ作成</h1>

    <div class="card">
        <div class="card-header">
            メルマガ登録フォーム作成
        </div>
        <div class="card-body">
            <form action="{{ route('admin.newsletter.store') }}" method="POST" enctype="multipart/form-data">
                @csrf

                <div class="mb-3">
                    <label for="title" class="form-label">メルマガタイトル <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('title') is-invalid @enderror" id="title" name="title" value="{{ old('title') }}" required>
                    <div class="form-text">メルマガのタイトルを入力してください。登録ページにも表示されます。</div>
                    @error('title')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="image" class="form-label">アイキャッチ画像</label>
                    <input type="file" class="form-control @error('image') is-invalid @enderror" id="image" name="image" accept="image/*">
                    <div class="form-text">登録ページに表示されるアイキャッチ画像をアップロードしてください。</div>
                    @error('image')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="gift_file" class="form-label">プレゼントファイル</label>
                    <input type="file" class="form-control @error('gift_file') is-invalid @enderror" id="gift_file" name="gift_file">
                    <div class="form-text">購読者にプレゼントするファイルをアップロードしてください。PDFやZIPファイルが推奨されます。</div>
                    @error('gift_file')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="gift_description" class="form-label">プレゼントの説明文</label>
                    <textarea class="form-control @error('gift_description') is-invalid @enderror" id="gift_description" name="gift_description" rows="5">{{ old('gift_description') }}</textarea>
                    <div class="form-text">プレゼントの内容や特徴について説明してください。登録ページに表示されます。</div>
                    @error('gift_description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="sales_letter" class="form-label">セールスレター（登録推奨文）</label>
                    <textarea class="form-control @error('sales_letter') is-invalid @enderror" id="sales_letter" name="sales_letter" rows="8">{{ old('sales_letter') }}</textarea>
                    <div class="form-text">メルマガ登録を促すセールスレターを入力してください。HTMLタグも使用可能です。</div>
                    @error('sales_letter')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="welcome_message" class="form-label">初回メール文章</label>
                    <textarea class="form-control @error('welcome_message') is-invalid @enderror" id="welcome_message" name="welcome_message" rows="8">{{ old('welcome_message') }}</textarea>
                    <div class="form-text">メルマガ登録時に送信される初回メールの文章を入力してください。未設定の場合はデフォルト文章が使用されます。</div>
                    @error('welcome_message')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="video_url" class="form-label">YouTube動画URL</label>
                    <input type="url" class="form-control @error('video_url') is-invalid @enderror" id="video_url" name="video_url" value="{{ old('video_url') }}">
                    <div class="form-text">YouTubeの埋め込み用URLを入力してください。例: https://www.youtube.com/embed/XXXX</div>
                    @error('video_url')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="video_file" class="form-label">動画ファイル</label>
                    <input type="file" class="form-control @error('video_file') is-invalid @enderror" id="video_file" name="video_file" accept="video/*">
                    <div class="form-text">YouTube以外の動画をアップロードする場合はこちらを使用してください。</div>
                    @error('video_file')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="landing_page_url" class="form-label">ランディングページURL</label>
                    <input type="url" class="form-control @error('landing_page_url') is-invalid @enderror" id="landing_page_url" name="landing_page_url" value="{{ old('landing_page_url') }}">
                    <div class="form-text">外部のランディングページがあればURLを入力してください。</div>
                    @error('landing_page_url')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="form_id" class="form-label">登録フォームID <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <input type="text" class="form-control @error('form_id') is-invalid @enderror" id="form_id" name="form_id" value="{{ old('form_id', $formId) }}" required readonly>
                        <button class="btn btn-outline-secondary" type="button" id="generate_form_id">再生成</button>
                    </div>
                    <div class="form-text">このIDは登録フォームのURLに使用されます。自動生成されますが、必要に応じて再生成できます。</div>
                    @error('form_id')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> 登録フォームを作成する
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            登録フォームの使い方
        </div>
        <div class="card-body">
            <p>登録フォームを作成すると、以下のような流れで購読者を収集できます：</p>
            <ol>
                <li>SNSやブログで登録フォームのURLを共有</li>
                <li>訪問者がフォームからメールアドレスと名前を登録</li>
                <li>登録完了後、プレゼントファイルのURLが自動でメール送信される</li>
            </ol>
            <p>登録フォームのURLは以下のようになります：</p>
            <div class="alert alert-info">
                <code>{{ url('/newsletter/subscribe/') }}/<span id="url_form_id">{{ $formId }}</span></code>
                <button class="btn btn-sm btn-outline-primary ms-2" id="copy_url" type="button">コピー</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // テキストエリアの高さを自動調整する関数
    function autoResizeTextarea(element) {
        element.style.height = 'auto';
        element.style.height = (element.scrollHeight) + 'px';
    }

    // 各テキストエリアに自動調整を適用
    const textareas = ['gift_description', 'sales_letter', 'welcome_message'];
    textareas.forEach(id => {
        const textarea = document.getElementById(id);
        if (textarea) {
            // 初期読み込み時に高さを調整
            textarea.style.height = (textarea.scrollHeight) + 'px';
            // 入力時に高さを調整
            textarea.addEventListener('input', function() {
                autoResizeTextarea(this);
            });
        }
    });

    // フォームIDの再生成
    document.getElementById('generate_form_id').addEventListener('click', function() {
        const randomId = generateRandomString(10);
        document.getElementById('form_id').value = randomId;
        document.getElementById('url_form_id').textContent = randomId;
    });

    // URLのコピー機能
    document.getElementById('copy_url').addEventListener('click', function() {
        const url = '{{ url("/newsletter/subscribe/") }}/' + document.getElementById('form_id').value;
        navigator.clipboard.writeText(url).then(function() {
            alert('URLをコピーしました');
        });
    });

    // ランダム文字列生成関数
    function generateRandomString(length) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
</script>
@endsection
