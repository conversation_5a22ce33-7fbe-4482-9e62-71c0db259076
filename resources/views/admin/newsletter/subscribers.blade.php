@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>メルマガ購読者一覧</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSubscriberModal">
            <i class="fas fa-plus me-1"></i> 購読者を追加
        </button>
    </div>

    <!-- 検索・フィルター -->
    <div class="card mb-4">
        <div class="card-body">
            <form action="{{ route('admin.newsletter.subscribers') }}" method="GET" class="row g-3">
                <div class="col-md-6">
                    <label for="search" class="form-label">検索</label>
                    <input type="text" class="form-control" id="search" name="search" value="{{ request('search') }}" placeholder="名前、メールアドレス">
                </div>
                <div class="col-md-4">
                    <label for="status" class="form-label">ステータス</label>
                    <select class="form-select" id="status" name="status">
                        <option value="all" {{ request('status') == 'all' ? 'selected' : '' }}>すべて</option>
                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>アクティブ</option>
                        <option value="unsubscribed" {{ request('status') == 'unsubscribed' ? 'selected' : '' }}>購読解除済み</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">検索</button>
                    <a href="{{ route('admin.newsletter.subscribers') }}" class="btn btn-secondary">リセット</a>
                </div>
            </form>
        </div>
    </div>

    <!-- 購読者一覧テーブル -->
    <div class="card">
        <div class="card-body">
            @if($subscribers->isEmpty())
                <p class="text-center my-5">購読者データがありません。</p>
            @else
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>名前</th>
                                <th>メールアドレス</th>
                                <th>所属（ニュースレター名）</th>
                                <th>ステータス</th>
                                <th>登録日</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($subscribers as $subscriber)
                                <tr>
                                    <td>{{ $subscriber->id }}</td>
                                    <td>{{ $subscriber->name ?? '未設定' }}</td>
                                    <td>{{ $subscriber->email }}</td>
                                    <td>
                                        @if($subscriber->newsletter)
                                            {{ $subscriber->newsletter->title }}
                                        @else
                                            <span class="text-muted">未設定</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($subscriber->status === 'active')
                                            <span class="badge bg-success">アクティブ</span>
                                        @else
                                            <span class="badge bg-secondary">購読解除済み</span>
                                        @endif
                                    </td>
                                    <td>{{ $subscriber->created_at->format('Y/m/d') }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-primary edit-subscriber"
                                                    data-id="{{ $subscriber->id }}"
                                                    data-name="{{ $subscriber->name }}"
                                                    data-email="{{ $subscriber->email }}"
                                                    data-status="{{ $subscriber->status }}"
                                                    data-newsletter-id="{{ $subscriber->newsletter_id ?? '' }}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ $subscriber->id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>

                                        <!-- 削除確認モーダル -->
                                        <div class="modal fade" id="deleteModal{{ $subscriber->id }}" tabindex="-1" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">削除確認</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p>{{ $subscriber->email }}の購読者データを削除しますか？</p>
                                                        <p class="text-danger">この操作は取り消せません。</p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">キャンセル</button>
                                                        <form action="{{ route('admin.newsletter.subscribers.destroy', ['subscriber' => $subscriber->id]) }}" method="POST">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-danger">削除する</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- ページネーション -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $subscribers->appends(request()->query())->links() }}
                </div>
            @endif
        </div>
    </div>
</div>

<!-- 購読者追加モーダル -->
<div class="modal fade" id="addSubscriberModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">購読者を追加</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('admin.newsletter.subscribers.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">名前</label>
                        <input type="text" class="form-control" id="name" name="name">
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">メールアドレス</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="newsletter_id" class="form-label">所属ニュースレター</label>
                        <select class="form-select" id="newsletter_id" name="newsletter_id">
                            <option value="">未設定</option>
                            @foreach($newsletters as $newsletter)
                                <option value="{{ $newsletter->id }}">{{ $newsletter->title }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">キャンセル</button>
                    <button type="submit" class="btn btn-primary">追加する</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 購読者編集モーダル -->
<div class="modal fade" id="editSubscriberModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">購読者を編集</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('admin.newsletter.subscribers.update', ['subscriber' => 0]) }}" method="POST" id="editSubscriberForm">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">名前</label>
                        <input type="text" class="form-control" id="edit_name" name="name">
                    </div>
                    <div class="mb-3">
                        <label for="edit_email" class="form-label">メールアドレス</label>
                        <input type="email" class="form-control" id="edit_email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_newsletter_id" class="form-label">所属ニュースレター</label>
                        <select class="form-select" id="edit_newsletter_id" name="newsletter_id">
                            <option value="">未設定</option>
                            @foreach($newsletters as $newsletter)
                                <option value="{{ $newsletter->id }}">{{ $newsletter->title }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_status" class="form-label">ステータス</label>
                        <select class="form-select" id="edit_status" name="status" required>
                            <option value="active">アクティブ</option>
                            <option value="unsubscribed">購読解除済み</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">キャンセル</button>
                    <button type="submit" class="btn btn-primary">更新する</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // 編集モーダルの表示
    document.querySelectorAll('.edit-subscriber').forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            const email = this.getAttribute('data-email');
            const status = this.getAttribute('data-status');
            const newsletterId = this.getAttribute('data-newsletter-id') || '';


            document.getElementById('edit_name').value = name;
            document.getElementById('edit_email').value = email;
            document.getElementById('edit_status').value = status;
            document.getElementById('edit_newsletter_id').value = newsletterId;

            // フォームのaction属性を更新
            const form = document.getElementById('editSubscriberForm');
            const baseUrl = form.action.split('/').slice(0, -1).join('/');
            form.action = `${baseUrl}/${id}`;

            const editModal = new bootstrap.Modal(document.getElementById('editSubscriberModal'));
            editModal.show();
        });
    });
</script>
@endsection
