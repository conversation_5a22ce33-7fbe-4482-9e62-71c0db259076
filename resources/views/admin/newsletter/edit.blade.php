@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <h1 class="mb-4">メルマガ編集</h1>

    <div class="card">
        <div class="card-header">
            メルマガ編集フォーム
        </div>
        <div class="card-body">
            <form action="{{ route('admin.newsletter.update', $newsletter) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                <div class="mb-3">
                    <label for="title" class="form-label">メルマガタイトル <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('title') is-invalid @enderror" id="title" name="title" value="{{ old('title', $newsletter->title) }}" required>
                    <div class="form-text">メルマガのタイトルを入力してください。登録ページにも表示されます。</div>
                    @error('title')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="image" class="form-label">アイキャッチ画像</label>
                    @if($newsletter->image_path)
                        <div class="mb-2">
                            <img src="{{ asset('storage/' . $newsletter->image_path) }}" alt="現在の画像" class="img-thumbnail" style="max-height: 200px;">
                            <p class="form-text">現在の画像: {{ $newsletter->image_path }}</p>
                        </div>
                    @endif
                    <input type="file" class="form-control @error('image') is-invalid @enderror" id="image" name="image" accept="image/*">
                    <div class="form-text">新しい画像をアップロードする場合のみ選択してください。</div>
                    @error('image')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="gift_file" class="form-label">プレゼントファイル</label>
                    @if($newsletter->gift_file_path)
                        <div class="mb-2">
                            <p class="form-text">現在のファイル: {{ $newsletter->gift_file_path }}</p>
                        </div>
                    @endif
                    <input type="file" class="form-control @error('gift_file') is-invalid @enderror" id="gift_file" name="gift_file">
                    <div class="form-text">新しいファイルをアップロードする場合のみ選択してください。</div>
                    @error('gift_file')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="gift_description" class="form-label">プレゼントの説明文</label>
                    <textarea class="form-control @error('gift_description') is-invalid @enderror" id="gift_description" name="gift_description" rows="5">{{ old('gift_description', $newsletter->gift_description) }}</textarea>
                    <div class="form-text">プレゼントの内容や特徴について説明してください。登録ページに表示されます。</div>
                    @error('gift_description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="sales_letter" class="form-label">セールスレター（登録推奨文）</label>
                    <textarea class="form-control @error('sales_letter') is-invalid @enderror" id="sales_letter" name="sales_letter" rows="8">{{ old('sales_letter', $newsletter->sales_letter) }}</textarea>
                    <div class="form-text">メルマガ登録を促すセールスレターを入力してください。HTMLタグも使用可能です。</div>
                    @error('sales_letter')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="welcome_message" class="form-label">初回メール文章</label>
                    <textarea class="form-control @error('welcome_message') is-invalid @enderror" id="welcome_message" name="welcome_message" rows="8">{{ old('welcome_message', $newsletter->welcome_message) }}</textarea>
                    <div class="form-text">メルマガ登録時に送信される初回メールの文章を入力してください。未設定の場合はデフォルト文章が使用されます。</div>
                    @error('welcome_message')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="video_url" class="form-label">YouTube動画URL</label>
                    <input type="url" class="form-control @error('video_url') is-invalid @enderror" id="video_url" name="video_url" value="{{ old('video_url', $newsletter->video_url) }}">
                    <div class="form-text">YouTubeの埋め込み用URLを入力してください。例: https://www.youtube.com/embed/XXXX</div>
                    @error('video_url')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="video_file" class="form-label">動画ファイル</label>
                    @if($newsletter->video_file_path)
                        <div class="mb-2">
                            <p class="form-text">現在のファイル: {{ $newsletter->video_file_path }}</p>
                        </div>
                    @endif
                    <input type="file" class="form-control @error('video_file') is-invalid @enderror" id="video_file" name="video_file" accept="video/*">
                    <div class="form-text">新しい動画をアップロードする場合のみ選択してください。</div>
                    @error('video_file')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="landing_page_url" class="form-label">ランディングページURL</label>
                    <input type="url" class="form-control @error('landing_page_url') is-invalid @enderror" id="landing_page_url" name="landing_page_url" value="{{ old('landing_page_url', $newsletter->landing_page_url) }}">
                    <div class="form-text">外部のランディングページがあればURLを入力してください。</div>
                    @error('landing_page_url')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="form_id" class="form-label">登録フォームID</label>
                    <input type="text" class="form-control" id="form_id" value="{{ $newsletter->form_id }}" readonly disabled>
                    <div class="form-text">このIDは登録フォームのURLに使用されます。編集では変更できません。</div>
                </div>

                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" {{ $newsletter->is_active ? 'checked' : '' }}>
                        <label class="form-check-label" for="is_active">
                            有効にする
                        </label>
                        <div class="form-text">チェックを外すと一時的に無効化されます。</div>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="{{ route('admin.newsletter.index') }}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i> 戻る
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> 更新する
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            登録フォームURL
        </div>
        <div class="card-body">
            <p>このメルマガの登録フォームURLは以下の通りです：</p>
            <div class="alert alert-info">
                <code>{{ url('/newsletter/subscribe/') }}/{{ $newsletter->form_id }}</code>
                <button class="btn btn-sm btn-outline-primary ms-2" id="copy_url" type="button">コピー</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // テキストエリアの高さを自動調整する関数
    function autoResizeTextarea(element) {
        element.style.height = 'auto';
        element.style.height = (element.scrollHeight) + 'px';
    }

    // 各テキストエリアに自動調整を適用
    const textareas = ['gift_description', 'sales_letter', 'welcome_message'];
    textareas.forEach(id => {
        const textarea = document.getElementById(id);
        if (textarea) {
            // 初期読み込み時に高さを調整
            textarea.style.height = (textarea.scrollHeight) + 'px';
            // 入力時に高さを調整
            textarea.addEventListener('input', function() {
                autoResizeTextarea(this);
            });
        }
    });

    // URLのコピー機能
    document.getElementById('copy_url').addEventListener('click', function() {
        const url = '{{ url("/newsletter/subscribe/") }}/{{ $newsletter->form_id }}';
        navigator.clipboard.writeText(url).then(function() {
            alert('URLをコピーしました');
        });
    });
</script>
@endsection
