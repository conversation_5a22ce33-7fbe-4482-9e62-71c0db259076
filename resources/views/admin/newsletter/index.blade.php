@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>メルマガ一覧</h1>
        <a href="{{ route('admin.newsletter.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> 新規作成
        </a>
    </div>

    <div class="alert alert-info mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-users me-2"></i> 現在のアクティブ購読者数: <strong>{{ $activeSubscribersCount }}人</strong>
            </div>
            <a href="{{ route('admin.newsletter.subscribers') }}" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-list me-1"></i> 購読者一覧を表示
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            @if($newsletters->isEmpty())
                <p class="text-center my-5">メルマガがありません。新規作成ボタンからメルマガを作成してください。</p>
            @else
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>タイトル</th>
                                <th>ステータス</th>
                                <th>購読者数</th>
                                <th>作成日</th>
                                <th>送信日</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($newsletters as $newsletter)
                                <tr>
                                    <td>{{ $newsletter->id }}</td>
                                    <td>{{ $newsletter->title }}</td>
                                    <td>
                                        @if($newsletter->sent_at)
                                            <span class="badge bg-success">送信済み</span>
                                        @elseif($newsletter->is_active)
                                            <span class="badge bg-primary">有効</span>
                                        @else
                                            <span class="badge bg-secondary">無効</span>
                                        @endif
                                    </td>
                                    <td>{{ $newsletter->subscribers_count }}人</td>
                                    <td>{{ $newsletter->created_at->format('Y/m/d H:i') }}</td>
                                    <td>{{ $newsletter->sent_at ? $newsletter->sent_at->format('Y/m/d H:i') : '-' }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ route('newsletter.landing', ['formId' => $newsletter->form_id]) }}"
                                               class="btn btn-sm btn-outline-primary" target="_blank">
                                                <i class="fas fa-eye"></i> 表示
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-secondary copy-url"
                                                    data-url="{{ route('newsletter.landing', ['formId' => $newsletter->form_id]) }}">
                                                <i class="fas fa-copy"></i> URLコピー
                                            </button>
                                            <a href="{{ route('admin.newsletter.edit', $newsletter) }}"
                                               class="btn btn-sm btn-outline-success">
                                                <i class="fas fa-edit"></i> 編集
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <div class="d-flex justify-content-center mt-4">
                    {{ $newsletters->links() }}
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // URLコピー機能
    document.querySelectorAll('.copy-url').forEach(button => {
        button.addEventListener('click', function() {
            const url = this.getAttribute('data-url');
            navigator.clipboard.writeText(url).then(function() {
                alert('URLをコピーしました');
            });
        });
    });
</script>
@endsection
