@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <h1 class="mb-4">メルマガ送信</h1>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    @if($newsletters->isEmpty())
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            送信可能なメルマガがありません。先にメルマガを作成してください。
                        </div>
                        <div class="text-center mt-4">
                            <a href="{{ route('admin.newsletter.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i> メルマガを作成する
                            </a>
                        </div>
                    @else
                        <form action="{{ route('admin.newsletter.send.post') }}" method="POST">
                            @csrf
                            
                            <div class="mb-3">
                                <label for="newsletter_id" class="form-label">送信するメルマガを選択</label>
                                <select class="form-select @error('newsletter_id') is-invalid @enderror" id="newsletter_id" name="newsletter_id" required>
                                    <option value="">選択してください</option>
                                    @foreach($newsletters as $newsletter)
                                        <option value="{{ $newsletter->id }}" {{ old('newsletter_id') == $newsletter->id ? 'selected' : '' }}>
                                            {{ $newsletter->subject }} (作成日: {{ $newsletter->created_at->format('Y/m/d H:i') }})
                                        </option>
                                    @endforeach
                                </select>
                                @error('newsletter_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="confirm_send" required>
                                    <label class="form-check-label" for="confirm_send">
                                        送信内容を確認し、{{ $subscribersCount }}人の購読者に送信します。
                                    </label>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <button type="submit" class="btn btn-primary" id="sendButton" disabled>
                                    <i class="fas fa-paper-plane me-1"></i> 送信する
                                </button>
                            </div>
                        </form>
                    @endif
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    送信情報
                </div>
                <div class="card-body">
                    <p><strong>購読者数:</strong> {{ $subscribersCount }}人</p>
                    <p><strong>送信元:</strong> {{ config('mail.from.address') }}</p>
                    <p><strong>送信元名:</strong> {{ config('mail.from.name') }}</p>
                    
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        送信したメルマガは取り消せません。内容を十分確認してから送信してください。
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    送信履歴
                </div>
                <div class="card-body">
                    <a href="{{ route('admin.newsletter.logs') }}" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-history me-1"></i> 送信ログを表示
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // 送信確認チェックボックスの処理
    const confirmCheckbox = document.getElementById('confirm_send');
    const sendButton = document.getElementById('sendButton');
    
    if (confirmCheckbox && sendButton) {
        confirmCheckbox.addEventListener('change', function() {
            sendButton.disabled = !this.checked;
        });
    }
    
    // メルマガ選択時のプレビュー表示（実装予定）
</script>
@endsection
