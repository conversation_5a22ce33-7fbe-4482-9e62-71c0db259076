<!DOCTYPE html>
<html lang="ja">
	@include('layouts.header')
<body>

	<header class="bg-light py-3">
		<div class="container container-lg d-flex justify-content-between align-items-center">
			<a href="{{ url('/') }}" class="navbar-brand" style="color: #30D5C8; font-weight: 700;">
				<img src="{{ url('images/tarotique.svg') }}" width="25" height="25" alt="Tarotique">Tarotique
				<script>
					document.addEventListener('DOMContentLoaded', function() {
						const setThemeColors = () => {
							const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
							document.getElementById('brandText').style.color = isDark ? '#FFD700' : '#000080';
							document.getElementById('brandLogo').style.filter = isDark
								? 'invert(79%) sepia(71%) saturate(628%) hue-rotate(359deg) brightness(103%) contrast(107%)'
								: 'invert(9%) sepia(100%) saturate(5919%) hue-rotate(241deg) brightness(97%) contrast(108%)';
						};

						// 初期設定
						setThemeColors();

						// テーマ変更を監視
						const observer = new MutationObserver((mutations) => {
							mutations.forEach((mutation) => {
								if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
									setThemeColors();
								}
							});
						});

						observer.observe(document.documentElement, {
							attributes: true,
							attributeFilter: ['data-theme']
						});
					});
				</script>
			</a>
			<div>
				@guest
					<a href="{{ route('login') }}" class="btn btn-outline-primary">ログイン</a>
					<a href="{{ route('register') }}" class="btn btn-primary">新規登録</a>
				@else
					<a href="{{ route('home') }}" class="btn btn-outline-primary">マイページ</a>
				@endguest

			</div>
		</div>
	</header>

	<main class="content">
		<div class="container-lg py-5">
			@yield('content')
		</div>
	</main>

	@include('layouts.footer')
</body>
</html>
