<footer class="bg-dark text-white py-4">
	<div class="container-lg">
		<div class="row">
			<div class="col-md-6 mb-3 mb-md-0">
				<h5>Tarotique</h5>
				<p class="small">タロットカードの世界へようこそ</p>
			</div>
			<div class="col-md-6 text-md-right">
				<div class="mb-3">
					<a href="{{ url('/') }}" class="text-white mx-2">HOME</a> |
					<a href="{{ url('/') }}" class="text-white mx-2">講座</a> |
					<a href="{{ url("faq") }}" class="text-white mx-2">FAQ</a> |
					<a href="{{ url("tokusho") }}" class="text-white mx-2">特定商取引法に基づく表記</a> |
					<a href="{{ url("privacy-policy") }}" class="text-white mx-2">プライバシーポリシー</a> |
					<a href="{{ url("terms-of-service") }}" class="text-white mx-2">利用規約</a> |
					<a href="{{ url('contact') }}" class="text-white mx-2">お問い合わせ</a>
				</div>
				<p class="small">&copy; {{ date('Y') }} Tarotique. All rights reserved.</p>
			</div>
		</div>
	</div>
</footer>

<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.0.7/dist/umd/popper.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

<script>
	// フラッシュメッセージを5秒後に自動的に消す
	$(document).ready(function() {
		setTimeout(function() {
			$('.alert').fadeOut('slow');
		}, 5000);
	});
</script>
